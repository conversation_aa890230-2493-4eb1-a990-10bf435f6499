<!DOCTYPE HTML>
<html lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="robots" content="index, follow">
        <title>aciTree using leaves links demo - A treeview control with jQuery</title>
        <meta name="description" content="A demo to show you how aciTree can be used with item links">
        <meta name="keywords" content="aciTree, treeview, control, tree view, javascript, jQuery">
        <link rel="stylesheet" type="text/css" href="../css/aciTree.css" media="all">
        <link rel="stylesheet" type="text/css" href="../css/demo.css" media="all">
        <script type="text/javascript" src="../js/jquery.min.js"></script>
        <script type="text/javascript" src="../js/jquery.aciPlugin.min.js"></script>
        <script type="text/javascript" src="../js/jquery.aciTree.min.js"></script>
    </head>
    <body>

        <div>

            <p>A demo to show how you can have a link for each tree leaf node (item without children).</p>
            <p>Note: a custom property named 'my-url' is used here to hold the URL for each item (see the JSON).</p>

            <p><a href="index.html" title="aciTree usage demo">back to index</a></p>

            <div id="tree" class="aciTree"><div>
                    <a style="font-size:10px" href="/source/php/niceJson.php?file=source/aciTree/json/sample.json" title="See the JSON data" target="_blank">see the JSON behind this tree</a>
                    <br>Sample tree</div></div>

            <div class="log">Tree Log... <a class="clear_log" style="font-size:10px" href="#" title="Clear the LOG" target="_blank">clear log</a>
                <div></div></div>

            <script class="code" type="text/javascript">

                $(function() {

                    // init the tree
                    $('#tree').aciTree({
                        ajax: {
                            url: '../json/sample.json'
                        },
                        itemHook: function(parent, item, itemData, level) {
                            if (!itemData.inode) {
                                // a custom item implementation to show a link
                                this.setLabel(item, {
                                    label: '<a href="' + itemData['my-url'] + '" target="_blank" title="Click to open ' + itemData['my-url'] + '">' + itemData.label + '</a>'
                                });
                            }
                        }
                    });

                });

            </script>

            <script type="text/javascript">

                $(function() {

                    var log = $('.log div');

                    // write to log
                    $('#tree').on('acitree', function(event, api, item, eventName, options) {
                        if (api.isItem(item)) {
                            log.prepend('<p>' + eventName + ' [' + api.getId(item) + ']</p>');
                        } else {
                            log.prepend('<p>' + eventName + ' [tree]</p>');
                        }
                    });

                    $('.clear_log').click(function() {
                        $('.log div').html('');
                        return false;
                    });

                });

            </script>

        </div>

        <script type="text/javascript">

            $(function() {

                $('script.code').each(function() {
                    $(this).before('<div style="clear:both;margin:10px 0 10px 0"><pre style="padding:20px;border:1px dashed #000;background:#f6f6f6;display:inline-block;"></pre></div>');
                    $(this).prev('div').find('pre').text($(this).html());
                });

            });

        </script>

    </body>
</html>