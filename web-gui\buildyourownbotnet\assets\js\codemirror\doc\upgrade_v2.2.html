<!doctype html>

<title>CodeMirror: Version 2.2 upgrade guide</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">

<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">2.2 upgrade guide</a>
  </ul>
</div>

<article>

<h2>Upgrading to v2.2</h2>

<p>There are a few things in the 2.2 release that require some care
when upgrading.</p>

<h3>No more default.css</h3>

<p>The default theme is now included
in <a href="../lib/codemirror.css"><code>codemirror.css</code></a>, so
you do not have to included it separately anymore. (It was tiny, so
even if you're not using it, the extra data overhead is negligible.)

<h3>Different key customization</h3>

<p>CodeMirror has moved to a system
where <a href="manual.html#option_keyMap">keymaps</a> are used to
bind behavior to keys. This means <a href="../demo/emacs.html">custom
bindings</a> are now possible.</p>

<p>Three options that influenced key
behavior, <code>tabMode</code>, <code>enterMode</code>,
and <code>smartHome</code>, are no longer supported. Instead, you can
provide custom bindings to influence the way these keys act. This is
done through the
new <a href="manual.html#option_extraKeys"><code>extraKeys</code></a>
option, which can hold an object mapping key names to functionality. A
simple example would be:</p>

<pre>  extraKeys: {
    "Ctrl-S": function(instance) { saveText(instance.getValue()); },
    "Ctrl-/": "undo"
  }</pre>

<p>Keys can be mapped either to functions, which will be given the
editor instance as argument, or to strings, which are mapped through
functions through the <code>CodeMirror.commands</code> table, which
contains all the built-in editing commands, and can be inspected and
extended by external code.</p>

<p>By default, the <code>Home</code> key is bound to
the <code>"goLineStartSmart"</code> command, which moves the cursor to
the first non-whitespace character on the line. You can set do this to
make it always go to the very start instead:</p>

<pre>  extraKeys: {"Home": "goLineStart"}</pre>

<p>Similarly, <code>Enter</code> is bound
to <code>"newlineAndIndent"</code> by default. You can bind it to
something else to get different behavior. To disable special handling
completely and only get a newline character inserted, you can bind it
to <code>false</code>:</p>

<pre>  extraKeys: {"Enter": false}</pre>

<p>The same works for <code>Tab</code>. If you don't want CodeMirror
to handle it, bind it to <code>false</code>. The default behaviour is
to indent the current line more (<code>"indentMore"</code> command),
and indent it less when shift is held (<code>"indentLess"</code>).
There are also <code>"indentAuto"</code> (smart indent)
and <code>"insertTab"</code> commands provided for alternate
behaviors. Or you can write your own handler function to do something
different altogether.</p>

<h3>Tabs</h3>

<p>Handling of tabs changed completely. The display width of tabs can
now be set with the <code>tabSize</code> option, and tabs can
be <a href="../demo/visibletabs.html">styled</a> by setting CSS rules
for the <code>cm-tab</code> class.</p>

<p>The default width for tabs is now 4, as opposed to the 8 that is
hard-wired into browsers. If you are relying on 8-space tabs, make
sure you explicitly set <code>tabSize: 8</code> in your options.</p>

</article>
