<!doctype html>

<title>CodeMirror: Compression Helper</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">

<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Compression helper</a>
  </ul>
</div>

<article>

<h2>Script compression helper</h2>

    <p>To optimize loading CodeMirror, especially when including a
    bunch of different modes, it is recommended that you combine and
    minify (and preferably also gzip) the scripts. This page makes
    those first two steps very easy. Simply select the version and
    scripts you need in the form below, and
    click <strong>Compress</strong> to download the minified script
    file.</p>

    <form id="form" action="http://marijnhaverbeke.nl/uglifyjs" method="post">
      <input type="hidden" id="download" name="download" value="codemirror-compressed.js"/>
      <p>Version: <select id="version" onchange="setVersion(this);" style="padding: 1px;">
        <option value="http://codemirror.net/">HEAD</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=3.21.0;f=">3.21</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=3.20.0;f=">3.20</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=3.19.0;f=">3.19</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=3.18.0;f=">3.18</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=3.16.0;f=">3.16</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=3.15.0;f=">3.15</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=3.14.0;f=">3.14</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=3.13.0;f=">3.13</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v3.12;f=">3.12</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v3.11;f=">3.11</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v3.1;f=">3.1</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v3.02;f=">3.02</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v3.01;f=">3.01</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v3.0;f=">3.0</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.38;f=">2.38</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.37;f=">2.37</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.36;f=">2.36</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.35;f=">2.35</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.34;f=">2.34</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.33;f=">2.33</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.32;f=">2.32</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.31;f=">2.31</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.3;f=">2.3</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.25;f=">2.25</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.24;f=">2.24</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.23;f=">2.23</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.22;f=">2.22</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.21;f=">2.21</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.2;f=">2.2</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.18;f=">2.18</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.16;f=">2.16</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.15;f=">2.15</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.13;f=">2.13</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.12;f=">2.12</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.11;f=">2.11</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.1;f=">2.1</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.02;f=">2.02</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.01;f=">2.01</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=v2.0;f=">2.0</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=beta2;f=">beta2</option>
        <option value="http://marijnhaverbeke.nl/git/codemirror?a=blob_plain;hb=beta1;f=">beta1</option>
      </select></p>

      <select multiple="multiple" size="20" name="code_url" style="width: 40em;" class="field" id="files">
        <optgroup label="CodeMirror Library">
          <option value="http://codemirror.net/lib/codemirror.js" selected>codemirror.js</option>
        </optgroup>
        <optgroup label="Modes">
          <option value="http://codemirror.net/mode/apl/apl.js">apl.js</option>
          <option value="http://codemirror.net/mode/clike/clike.js">clike.js</option>
          <option value="http://codemirror.net/mode/clojure/clojure.js">clojure.js</option>
          <option value="http://codemirror.net/mode/cobol/cobol.js">cobol.js</option>
          <option value="http://codemirror.net/mode/coffeescript/coffeescript.js">coffeescript.js</option>
          <option value="http://codemirror.net/mode/commonlisp/commonlisp.js">commonlisp.js</option>
          <option value="http://codemirror.net/mode/css/css.js">css.js</option>
          <option value="http://codemirror.net/mode/d/d.js">d.js</option>
          <option value="http://codemirror.net/mode/diff/diff.js">diff.js</option>
          <option value="http://codemirror.net/mode/dtd/dtd.js">dtd.js</option>
          <option value="http://codemirror.net/mode/ecl/ecl.js">ecl.js</option>
          <option value="http://codemirror.net/mode/eiffel/eiffel.js">eiffel.js</option>
          <option value="http://codemirror.net/mode/erlang/erlang.js">erlang.js</option>
          <option value="http://codemirror.net/mode/fortran/fortran.js">fortran.js</option>
          <option value="http://codemirror.net/mode/gfm/gfm.js">gfm.js</option>
          <option value="http://codemirror.net/mode/gas/gas.js">gas.js</option>
          <option value="http://codemirror.net/mode/gherkin/gherkin.js">gherkin.js</option>
          <option value="http://codemirror.net/mode/go/go.js">go.js</option>
          <option value="http://codemirror.net/mode/groovy/groovy.js">groovy.js</option>
          <option value="http://codemirror.net/mode/haml/haml.js">haml.js</option>
          <option value="http://codemirror.net/mode/haskell/haskell.js">haskell.js</option>
          <option value="http://codemirror.net/mode/haxe/haxe.js">haxe.js</option>
          <option value="http://codemirror.net/mode/htmlembedded/htmlembedded.js">htmlembedded.js</option>
          <option value="http://codemirror.net/mode/htmlmixed/htmlmixed.js">htmlmixed.js</option>
          <option value="http://codemirror.net/mode/http/http.js">http.js</option>
          <option value="http://codemirror.net/mode/jade/jade.js">jade.js</option>
          <option value="http://codemirror.net/mode/javascript/javascript.js">javascript.js</option>
          <option value="http://codemirror.net/mode/jinja2/jinja2.js">jinja2.js</option>
          <option value="http://codemirror.net/mode/julia/julia.js">jinja2.js</option>
          <option value="http://codemirror.net/mode/less/less.js">less.js</option>
          <option value="http://codemirror.net/mode/livescript/livescript.js">livescript.js</option>
          <option value="http://codemirror.net/mode/lua/lua.js">lua.js</option>
          <option value="http://codemirror.net/mode/markdown/markdown.js">markdown.js</option>
          <option value="http://codemirror.net/mode/mirc/mirc.js">mirc.js</option>
          <option value="http://codemirror.net/mode/mllike/mllike.js">mllike.js</option>
          <option value="http://codemirror.net/mode/nginx/nginx.js">nginx.js</option>
          <option value="http://codemirror.net/mode/ntriples/ntriples.js">ntriples.js</option>
          <option value="http://codemirror.net/mode/octave/octave.js">octave.js</option>
          <option value="http://codemirror.net/mode/pascal/pascal.js">pascal.js</option>
          <option value="http://codemirror.net/mode/pegjs/pegjs.js">pegjs.js</option>
          <option value="http://codemirror.net/mode/perl/perl.js">perl.js</option>
          <option value="http://codemirror.net/mode/php/php.js">php.js</option>
          <option value="http://codemirror.net/mode/pig/pig.js">pig.js</option>
          <option value="http://codemirror.net/mode/properties/properties.js">properties.js</option>
          <option value="http://codemirror.net/mode/python/python.js">python.js</option>
          <option value="http://codemirror.net/mode/q/q.js">q.js</option>
          <option value="http://codemirror.net/mode/r/r.js">r.js</option>
          <option value="http://codemirror.net/mode/rpm/changes/changes.js">rpm/changes.js</option>
          <option value="http://codemirror.net/mode/rpm/spec/spec.js">rpm/spec.js</option>
          <option value="http://codemirror.net/mode/rst/rst.js">rst.js</option>
          <option value="http://codemirror.net/mode/ruby/ruby.js">ruby.js</option>
          <option value="http://codemirror.net/mode/rust/rust.js">rust.js</option>
          <option value="http://codemirror.net/mode/sass/sass.js">sass.js</option>
          <option value="http://codemirror.net/mode/scala/scala.js">scala.js</option>
          <option value="http://codemirror.net/mode/scheme/scheme.js">scheme.js</option>
          <option value="http://codemirror.net/mode/shell/shell.js">shell.js</option>
          <option value="http://codemirror.net/mode/sieve/sieve.js">sieve.js</option>
          <option value="http://codemirror.net/mode/smalltalk/smalltalk.js">smalltalk.js</option>
          <option value="http://codemirror.net/mode/smarty/smarty.js">smarty.js</option>
          <option value="http://codemirror.net/mode/smartymixed/smartymixed.js">smartymixed.js</option>
          <option value="http://codemirror.net/mode/sql/sql.js">sql.js</option>
          <option value="http://codemirror.net/mode/sparql/sparql.js">sparql.js</option>
          <option value="http://codemirror.net/mode/stex/stex.js">stex.js</option>
          <option value="http://codemirror.net/mode/tcl/tcl.js">tcl.js</option>
          <option value="http://codemirror.net/mode/tiddlywiki/tiddlywiki.js">tiddlywiki.js</option>
          <option value="http://codemirror.net/mode/tiki/tiki.js">tiki.js</option>
          <option value="http://codemirror.net/mode/toml/toml.js">toml.js</option>
          <option value="http://codemirror.net/mode/turtle/turtle.js">turtle.js</option>
          <option value="http://codemirror.net/mode/vb/vb.js">vb.js</option>
          <option value="http://codemirror.net/mode/vbscript/vbscript.js">vbscript.js</option>
          <option value="http://codemirror.net/mode/velocity/velocity.js">velocity.js</option>
          <option value="http://codemirror.net/mode/verilog/verilog.js">verilog.js</option>
          <option value="http://codemirror.net/mode/xml/xml.js">xml.js</option>
          <option value="http://codemirror.net/mode/xquery/xquery.js">xquery.js</option>
          <option value="http://codemirror.net/mode/yaml/yaml.js">yaml.js</option>
          <option value="http://codemirror.net/mode/z80/z80.js">z80.js</option>
        </optgroup>
        <optgroup label="Add-ons">
          <option value="http://codemirror.net/addon/selection/active-line.js">active-line.js</option>
          <option value="http://codemirror.net/addon/hint/anyword-hint.js">anyword-hint.js</option>
          <option value="http://codemirror.net/addon/fold/brace-fold.js">brace-fold.js</option>
          <option value="http://codemirror.net/addon/edit/closebrackets.js">closebrackets.js</option>
          <option value="http://codemirror.net/addon/edit/closetag.js">closetag.js</option>
          <option value="http://codemirror.net/addon/runmode/colorize.js">colorize.js</option>
          <option value="http://codemirror.net/addon/comment/comment.js">comment.js</option>
          <option value="http://codemirror.net/addon/fold/comment-fold.js">comment-fold.js</option>
          <option value="http://codemirror.net/addon/comment/continuecomment.js">continuecomment.js</option>
          <option value="http://codemirror.net/addon/edit/continuelist.js">continuelist.js</option>
          <option value="http://codemirror.net/addon/hint/css-hint.js">css-hint.js</option>
          <option value="http://codemirror.net/addon/dialog/dialog.js">dialog.js</option>
          <option value="http://codemirror.net/addon/fold/foldcode.js">foldcode.js</option>
          <option value="http://codemirror.net/addon/fold/foldgutter.js">foldgutter.js</option>
          <option value="http://codemirror.net/addon/display/fullscreen.js">fullscreen.js</option>
          <option value="http://codemirror.net/addon/wrap/hardwrap.js">hardwrap.js</option>
          <option value="http://codemirror.net/addon/hint/html-hint.js">html-hint.js</option>
          <option value="http://codemirror.net/addon/fold/indent-fold.js">indent-fold.js</option>
          <option value="http://codemirror.net/addon/hint/javascript-hint.js">javascript-hint.js</option>
          <option value="http://codemirror.net/addon/lint/javascript-lint.js">javascript-lint.js</option>
          <option value="http://codemirror.net/addon/lint/json-lint.js">json-lint.js</option>
          <option value="http://codemirror.net/addon/lint/lint.js">lint.js</option>
          <option value="http://codemirror.net/addon/mode/loadmode.js">loadmode.js</option>
          <option value="http://codemirror.net/addon/selection/mark-selection.js">mark-selection.js</option>
          <option value="http://codemirror.net/addon/search/match-highlighter.js">match-highlighter.js</option>
          <option value="http://codemirror.net/addon/edit/matchbrackets.js">matchbrackets.js</option>
          <option value="http://codemirror.net/addon/edit/matchtags.js">matchtags.js</option>
          <option value="http://codemirror.net/addon/merge/merge.js">merge.js</option>
          <option value="http://codemirror.net/addon/mode/multiplex.js">multiplex.js</option>
          <option value="http://codemirror.net/addon/mode/overlay.js">overlay.js</option>
          <option value="http://codemirror.net/addon/hint/pig-hint.js">pig-hint.js</option>
          <option value="http://codemirror.net/addon/display/placeholder.js">placeholder.js</option>
          <option value="http://codemirror.net/addon/hint/python-hint.js">python-hint.js</option>
          <option value="http://codemirror.net/addon/runmode/runmode.js">runmode.js</option>
          <option value="http://codemirror.net/addon/runmode/runmode.node.js">runmode.node.js</option>
          <option value="http://codemirror.net/addon/runmode/runmode-standalone.js">runmode-standalone.js</option>
          <option value="http://codemirror.net/addon/search/search.js">search.js</option>
          <option value="http://codemirror.net/addon/search/searchcursor.js">searchcursor.js</option>
          <option value="http://codemirror.net/addon/hint/show-hint.js">show-hint.js</option>
          <option value="http://codemirror.net/addon/hint/sql-hint.js">sql-hint.js</option>
          <option value="http://codemirror.net/addon/edit/trailingspace.js">trailingspace.js</option>
          <option value="http://codemirror.net/addon/tern/tern.js">tern.js</option>
          <option value="http://codemirror.net/addon/fold/xml-fold.js">xml-fold.js</option>
          <option value="http://codemirror.net/addon/hint/xml-hint.js">xml-hint.js</option>
        </optgroup>
        <optgroup label="Keymaps">
          <option value="http://codemirror.net/keymap/emacs.js">emacs.js</option>
          <option value="http://codemirror.net/keymap/vim.js">vim.js</option>
        </optgroup>
      </select>

      <p>
        <button type="submit">Compress</button> with <a href="http://github.com/mishoo/UglifyJS/">UglifyJS</a>
      </p>

      <p>Custom code to add to the compressed file:<textarea name="js_code" style="width: 100%; height: 15em;" class="field"></textarea></p>
    </form>

    <script type="text/javascript">
      function setVersion(ver) {
        var urlprefix = ver.options[ver.selectedIndex].value;
        var select = document.getElementById("files"), m;
        for (var optgr = select.firstChild; optgr; optgr = optgr.nextSibling)
          for (var opt = optgr.firstChild; opt; opt = opt.nextSibling) {
            if (opt.nodeName != "OPTION")
              continue;
            else if (m = opt.value.match(/^http:\/\/codemirror.net\/(.*)$/))
              opt.value = urlprefix + m[1];
            else if (m = opt.value.match(/http:\/\/marijnhaverbeke.nl\/git\/codemirror\?a=blob_plain;hb=[^;]+;f=(.*)$/))
              opt.value = urlprefix + m[1];
          }
       }
    </script>

</article>
