<!doctype html>

<title>CodeMirror: Match Selection Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/search/searchcursor.js"></script>
<script src="../addon/selection/mark-selection.js"></script>
<style type="text/css">
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
      .CodeMirror-selected  { background-color: blue !important; }
      .CodeMirror-selectedtext { color: white; }
    </style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Match Selection</a>
  </ul>
</div>

<article>
<h2>Match Selection Demo</h2>
<form><textarea id="code" name="code">Select something from here.
You'll see that the selection's foreground color changes to white!
Since, by default, CodeMirror only puts an independent "marker" layer
behind the text, you'll need something like this to change its colour.</textarea></form>

    <script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  lineNumbers: true,
  styleSelectedText: true
});
</script>

    <p>Simple addon to easily mark (and style) selected text.</p>

  </article>
