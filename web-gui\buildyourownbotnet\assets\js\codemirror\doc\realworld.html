<!doctype html>

<title>CodeMirror: Real-world Uses</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">

<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Real-world uses</a>
  </ul>
</div>

<article>

<h2>CodeMirror real-world uses</h2>

    <p><a href="mailto:<EMAIL>">Contact me</a> if you'd like
    your project to be added to this list.</p>

    <ul>
      <li><a href="http://brackets.io">Adobe Brackets</a> (code editor)</li>
      <li><a href="http://amber-lang.net/">Amber</a> (JavaScript-based Smalltalk system)</li>
      <li><a href="http://apeye.org/">APEye</a> (tool for testing &amp; documenting APIs)</li>
      <li><a href="http://blog.bitbucket.org/2013/05/14/edit-your-code-in-the-cloud-with-bitbucket/">Bitbucket</a> (code hosting)</li>
      <li><a href="http://buzz.blogger.com/2013/04/improvements-to-blogger-template-html.html">Blogger's template editor</a></li>
      <li><a href="http://bluegriffon.org/">BlueGriffon</a> (HTML editor)</li>
      <li><a href="http://cargocollective.com/">Cargo Collective</a> (creative publishing platform)</li>
      <li><a href="http://clickhelp.co/">ClickHelp</a> (technical writing tool)</li>
      <li><a href="http://complete-ly.appspot.com/playground/code.playground.html">Complete.ly playground</a></li>
      <li><a href="http://drupal.org/project/cpn">Code per Node</a> (Drupal module)</li>
      <li><a href="http://www.codebugapp.com/">Codebug</a> (PHP Xdebug front-end)</li>
      <li><a href="https://github.com/angelozerr/CodeMirror-Eclipse">CodeMirror Eclipse</a> (embed CM in Eclipse)</li>
      <li><a href="http://emmet.io/blog/codemirror-movie/">CodeMirror movie</a> (scripted editing demos)</li>
      <li><a href="http://code.google.com/p/codemirror2-gwt/">CodeMirror2-GWT</a> (Google Web Toolkit wrapper)</li>
      <li><a href="http://www.crunchzilla.com/code-monster">Code Monster</a> & <a href="http://www.crunchzilla.com/code-maven">Code Maven</a> (learning environment)</li>
      <li><a href="http://codepen.io">Codepen</a> (gallery of animations)</li>
      <li><a href="http://sasstwo.codeschool.com/levels/1/challenges/1">Code School</a> (online tech learning environment)</li>
      <li><a href="http://code-snippets.bungeshea.com/">Code Snippets</a> (WordPress snippet management plugin)</li>
      <li><a href="http://antonmi.github.io/code_together/">Code together</a> (collaborative editing)</li>
      <li><a href="http://codev.it/">Codev</a> (collaborative IDE)</li>
      <li><a href="http://www.codezample.com">CodeZample</a> (code snippet sharing)</li>
      <li><a href="http://codio.com">Codio</a> (Web IDE)</li>
      <li><a href="http://ot.substance.io/demo/">Collaborative CodeMirror demo</a> (CodeMirror + operational transforms)</li>
      <li><a href="http://www.communitycodecamp.com/">Community Code Camp</a> (code snippet sharing)</li>
      <li><a href="http://www.compilejava.net/">compilejava.net</a> (online Java sandbox)</li>
      <li><a href="http://www.ckwnc.com/">CKWNC</a> (UML editor)</li>
      <li><a href="http://www.crudzilla.com/">Crudzilla</a> (self-hosted web IDE)</li>
      <li><a href="http://cssdeck.com/">CSSDeck</a> (CSS showcase)</li>
      <li><a href="http://ireneros.com/deck/deck.js-codemirror/introduction/#textarea-code">Deck.js integration</a> (slides with editors)</li>
      <li><a href="http://www.dbninja.com">DbNinja</a> (MySQL access interface)</li>
      <li><a href="https://chat.echoplex.us/">Echoplexus</a> (chat and collaborative coding)</li>
      <li><a href="http://elm-lang.org/Examples.elm">Elm language examples</a></li>
      <li><a href="http://eloquentjavascript.net/chapter1.html">Eloquent JavaScript</a> (book)</li>
      <li><a href="http://emmet.io">Emmet</a> (fast XML editing)</li>
      <li><a href="http://www.fastfig.com/">Fastfig</a> (online computation/math tool)</li>
      <li><a href="https://metacpan.org/module/Farabi">Farabi</a> (modern Perl IDE)</li>
      <li><a href="http://blog.pamelafox.org/2012/02/interactive-html5-slides-with-fathomjs.html">FathomJS integration</a> (slides with editors, again)</li>
      <li><a href="http://fiddlesalad.com/">Fiddle Salad</a> (web development environment)</li>
      <li><a href="http://www.firepad.io">Firepad</a> (collaborative text editor)</li>
      <li><a href="https://code.google.com/p/gerrit/">Gerrit</a>'s diff view</a></li>
      <li><a href="http://tour.golang.org">Go language tour</a></li>
      <li><a href="https://github.com/github/android">GitHub's Android app</a></li>
      <li><a href="https://script.google.com/">Google Apps Script</a></li>
      <li><a href="http://web.uvic.ca/~siefkenj/graphit/graphit.html">Graphit</a> (function graphing)</li>
      <li><a href="http://www.handcraft.com/">Handcraft</a> (HTML prototyping)</li>
      <li><a href="http://try.haxe.org">Haxe</a> (Haxe Playground) </li>
      <li><a href="http://haxpad.com/">HaxPad</a> (editor for Win RT)</li>
      <li><a href="http://megafonweblab.github.com/histone-javascript/">Histone template engine playground</a></li>
      <li><a href="http://icecoder.net">ICEcoder</a> (web IDE)</li>
      <li><a href="http://www.janvas.com/">Janvas</a> (vector graphics editor)</li>
      <li><a href="http://extensions.joomla.org/extensions/edition/editors/8723">Joomla plugin</a></li>
      <li><a href="http://jqfundamentals.com/">jQuery fundamentals</a> (interactive tutorial)</li>
      <li><a href="http://jsbin.com">jsbin.com</a> (JS playground)</li>
      <li><a href="http://jsfiddle.com">jsfiddle.com</a> (another JS playground)</li>
      <li><a href="http://www.jshint.com/">JSHint</a> (JS linter)</li>
      <li><a href="http://jumpseller.com/">Jumpseller</a> (online store builder)</li>
      <li><a href="http://kl1p.com/cmtest/1">kl1p</a> (paste service)</li>
      <li><a href="http://kodtest.com/">Kodtest</a> (HTML/JS/CSS playground)</li>
      <li><a href="http://lighttable.com/">Light Table</a> (experimental IDE)</li>
      <li><a href="http://liveweave.com/">Liveweave</a> (HTML/CSS/JS scratchpad)</li>
      <li><a href="http://marklighteditor.com/">Marklight editor</a> (lightweight markup editor)</li>
      <li><a href="http://www.mergely.com/">Mergely</a> (interactive diffing)</li>
      <li><a href="http://www.iunbug.com/mihtool">MIHTool</a> (iOS web-app debugging tool)</li>
      <li><a href="http://mongo-mapreduce-webbrowser.opensagres.cloudbees.net/">Mongo MapReduce WebBrowser</a></li>
      <li><a href="http://mvcplayground.apphb.com/">MVC Playground</a></li>
      <li><a href="https://www.my2ndgeneration.com/">My2ndGeneration</a> (social coding)</li>
      <li><a href="http://www.navigatecms.com">Navigate CMS</a></li>
      <li><a href="https://github.com/soliton4/nodeMirror">nodeMirror</a> (IDE project)</li>
      <li><a href="https://notex.ch">NoTex</a> (rST authoring)</li>
      <li><a href="http://oakoutliner.com">Oak</a> (online outliner)</li>
      <li><a href="http://clrhome.org/asm/">ORG</a> (z80 assembly IDE)</li>
      <li><a href="https://github.com/mamacdon/orion-codemirror">Orion-CodeMirror integration</a> (running CodeMirror modes in Orion)</li>
      <li><a href="http://paperjs.org/">Paper.js</a> (graphics scripting)</li>
      <li><a href="http://prinbit.com/">PrinBit</a> (collaborative coding tool)</li>
      <li><a href="http://prose.io/">Prose.io</a> (github content editor)</li>
      <li><a href="http://www.puzzlescript.net/">Puzzlescript</a> (puzzle game engine)</li>
      <li><a href="http://ql.io/">ql.io</a> (http API query helper)</li>
      <li><a href="http://qyapp.com">QiYun web app platform</a></li>
      <li><a href="http://ariya.ofilabs.com/2011/09/hybrid-webnative-desktop-codemirror.html">Qt+Webkit integration</a> (building a desktop CodeMirror app)</li>
      <li><a href="http://www.quivive-file-manager.com">Quivive File Manager</a></li>
      <li><a href="http://rascalmicro.com/docs/basic-tutorial-getting-started.html">Rascal</a> (tiny computer)</li>
      <li><a href="https://www.realtime.io/">RealTime.io</a> (Internet-of-Things infrastructure)</li>
      <li><a href="https://www.shadertoy.com/">Shadertoy</a> (shader sharing)</li>
      <li><a href="http://www.sketchpatch.net/labs/livecodelabIntro.html">sketchPatch Livecodelab</a></li>
      <li><a href="http://www.skulpt.org/">Skulpt</a> (in-browser Python environment)</li>
      <li><a href="http://snippets.pro/">Snippets.pro</a> (code snippet sharing)</li>
      <li><a href="http://www.solidshops.com/">SolidShops</a> (hosted e-commerce platform)</li>
      <li><a href="http://sqlfiddle.com">SQLFiddle</a> (SQL playground)</li>
      <li><a href="https://thefiletree.com">The File Tree</a> (collab editor)</li>
      <li><a href="http://www.mapbox.com/tilemill/">TileMill</a> (map design tool)</li>
      <li><a href="http://www.toolsverse.com/products/data-explorer/">Toolsverse Data Explorer</a> (database management)</li>
      <li><a href="http://enjalot.com/tributary/2636296/sinwaves.js">Tributary</a> (augmented editing)</li>
      <li><a href="http://blog.englard.net/post/39608000629/codeintumblr">Tumblr code highlighting shim</a></li>
      <li><a href="http://turbopy.com/">TurboPY</a> (web publishing framework)</li>
      <li><a href="http://uicod.com/">uiCod</a> (animation demo gallery and sharing)</li>
      <li><a href="http://cruise.eecs.uottawa.ca/umpleonline/">UmpleOnline</a> (model-oriented programming tool)</li>
      <li><a href="https://upsource.jetbrains.com/#idea/view/923f30395f2603cd9f42a32bcafd13b6c28de0ff/plugins/groovy/src/org/jetbrains/plugins/groovy/intentions/style/ReplaceAbstractClassInstanceByMapIntention.java">Upsource</a> (code viewer)</li>
      <li><a href="https://github.com/brettz9/webappfind">webappfind</a> (windows file bindings for webapps)</li>
      <li><a href="http://www.webglacademy.com/">WebGL academy</a> (learning WebGL)</li>
      <li><a href="http://webglplayground.net/">WebGL playground</a></li>
      <li><a href="https://www.webkit.org/blog/2518/state-of-web-inspector/#source-code">WebKit Web inspector</a></li>
      <li><a href="http://www.wescheme.org/">WeScheme</a> (learning tool)</li>
      <li><a href="http://wordpress.org/extend/plugins/codemirror-for-codeeditor/">WordPress plugin</a></li>
      <li><a href="https://www.writelatex.com">writeLaTeX</a> (Collaborative LaTeX Editor)</li>
      <li><a href="http://www.xosystem.org/home/<USER>/xosystem_website/xoside_EN.php">XOSide</a> (online editor)</li>
      <li><a href="http://videlibri.sourceforge.net/cgi-bin/xidelcgi">XQuery tester</a></li>
      <li><a href="http://q42jaap.github.io/xsd2codemirror/">xsd2codemirror</a> (convert XSD to CM XML completion info)</li>
    </ul>

</article>

