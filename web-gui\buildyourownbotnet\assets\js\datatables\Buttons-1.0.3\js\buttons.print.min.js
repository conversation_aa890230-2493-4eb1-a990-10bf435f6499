(function(f,h){var d=document.createElement("a");h.ext.buttons.print={className:"buttons-print",text:function(b){return b.i18n("buttons.print","Print")},action:function(b,a,g,c){b=a.buttons.exportData(c.exportOptions);g=function(a,b){for(var i="<tr>",c=0,d=a.length;c<d;c++)i+="<"+b+">"+a[c]+"</"+b+">";return i+"</tr>"};a='<table class="'+a.table().node().className+'">';c.header&&(a+="<thead>"+g(b.header,"th")+"</thead>");for(var a=a+"<tbody>",j=0,h=b.body.length;j<h;j++)a+=g(b.body[j],"td");a+="</tbody>";
c.footer&&(a+="<thead>"+g(b.footer,"th")+"</thead>");var e=window.open("",""),b=c.title.replace("*",f("title").text());e.document.close();var k="<title>"+b+"</title>";f("style, link").each(function(){var a=k,b=f(this).clone()[0],c;"link"===b.nodeName.toLowerCase()&&(d.href=b.href,c=d.host,-1===c.indexOf("/")&&0!==d.pathname.indexOf("/")&&(c+="/"),b.href=d.protocol+"//"+c+d.pathname+d.search);k=a+b.outerHTML});f(e.document.head).html(k);f(e.document.body).html("<h1>"+b+"</h1><div>"+c.message+"</div>"+
a);c.customize&&c.customize(e);setTimeout(function(){c.autoPrint&&(e.print(),e.close())},250)},title:"*",message:"",exportOptions:{},header:!0,footer:!1,autoPrint:!0,customize:null}})(jQuery,jQuery.fn.dataTable);
