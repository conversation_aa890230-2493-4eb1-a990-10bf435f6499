// Kludge in HTML5 tag recognition in IE8
document.createElement("section");
document.createElement("article");

(function() {
  var pending = false, prevVal = null;

  function updateSoon() {
    if (!pending) {
      pending = true;
      setTimeout(update, 250);
    }
  }

  function update() {
    pending = false;
    var marks = document.getElementById("nav").getElementsByTagName("a"), found;
    for (var i = 0; i < marks.length; ++i) {
      var mark = marks[i], m;
      if (mark.getAttribute("data-default")) {
        if (found == null) found = i;
      } else if (m = mark.href.match(/#(.*)/)) {
        var ref = document.getElementById(m[1]);
        if (ref && ref.getBoundingClientRect().top < 50)
          found = i;
      }
    }
    if (found != null && found != prevVal) {
      prevVal = found;
      var lis = document.getElementById("nav").getElementsByTagName("li");
      for (var i = 0; i < lis.length; ++i) lis[i].className = "";
      for (var i = 0; i < marks.length; ++i) {
        if (found == i) {
          marks[i].className = "active";
          for (var n = marks[i]; n; n = n.parentNode)
            if (n.nodeName == "LI") n.className = "active";
        } else {
          marks[i].className = "";
        }
      }
    }
  }

  if (window.addEventListener) {
    window.addEventListener("scroll", updateSoon);
    window.addEventListener("load", updateSoon);
  }
})();
