<!doctype html>

<title>CodeMirror: Version 3 upgrade guide</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">
<script src="../lib/codemirror.js"></script>
<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../addon/runmode/runmode.js"></script>
<script src="../addon/runmode/colorize.js"></script>
<script src="../mode/javascript/javascript.js"></script>
<script src="../mode/xml/xml.js"></script>
<script src="../mode/css/css.js"></script>
<script src="../mode/htmlmixed/htmlmixed.js"></script>
<script src="activebookmark.js"></script>

<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#upgrade">Upgrade guide</a>
    <li><a href="#dom">DOM structure</a></li>
    <li><a href="#gutters">Gutter model</a></li>
    <li><a href="#events">Event handling</a></li>
    <li><a href="#marktext">markText method arguments</a></li>
    <li><a href="#folding">Line folding</a></li>
    <li><a href="#lineclass">Line CSS classes</a></li>
    <li><a href="#positions">Position properties</a></li>
    <li><a href="#matchbrackets">Bracket matching</a></li>
    <li><a href="#modes">Mode management</a></li>
    <li><a href="#new">New features</a></li>
  </ul>
</div>

<article>

<h2 id=upgrade>Upgrading to version 3</h2>

<p>Version 3 does not depart too much from 2.x API, and sites that use
CodeMirror in a very simple way might be able to upgrade without
trouble. But it does introduce a number of incompatibilities. Please
at least skim this text before upgrading.</p>

<p>Note that <strong>version 3 drops full support for Internet
Explorer 7</strong>. The editor will mostly work on that browser, but
it'll be significantly glitchy.</p>

<section id=dom>
  <h2>DOM structure</h2>

<p>This one is the most likely to cause problems. The internal
structure of the editor has changed quite a lot, mostly to implement a
new scrolling model.</p>

<p>Editor height is now set on the outer wrapper element (CSS
class <code>CodeMirror</code>), not on the scroller element
(<code>CodeMirror-scroll</code>).</p>

<p>Other nodes were moved, dropped, and added. If you have any code
that makes assumptions about the internal DOM structure of the editor,
you'll have to re-test it and probably update it to work with v3.</p>

<p>See the <a href="manual.html#styling">styling section</a> of the
manual for more information.</p>
</section>
<section id=gutters>
  <h2>Gutter model</h2>

<p>In CodeMirror 2.x, there was a single gutter, and line markers
created with <code>setMarker</code> would have to somehow coexist with
the line numbers (if present). Version 3 allows you to specify an
array of gutters, <a href="manual.html#option_gutters">by class
name</a>,
use <a href="manual.html#setGutterMarker"><code>setGutterMarker</code></a>
to add or remove markers in individual gutters, and clear whole
gutters
with <a href="manual.html#clearGutter"><code>clearGutter</code></a>.
Gutter markers are now specified as DOM nodes, rather than HTML
snippets.</p>

<p>The gutters no longer horizontally scrolls along with the content.
The <code>fixedGutter</code> option was removed (since it is now the
only behavior).</p>

<pre data-lang="text/html">
&lt;style>
  /* Define a gutter style */
  .note-gutter { width: 3em; background: cyan; }
&lt;/style>
&lt;script>
  // Create an instance with two gutters -- line numbers and notes
  var cm = new CodeMirror(document.body, {
    gutters: ["note-gutter", "CodeMirror-linenumbers"],
    lineNumbers: true
  });
  // Add a note to line 0
  cm.setGutterMarker(0, "note-gutter", document.createTextNode("hi"));
&lt;/script>
</pre>
</section>
<section id=events>
  <h2>Event handling</h2>

<p>Most of the <code>onXYZ</code> options have been removed. The same
effect is now obtained by calling
the <a href="manual.html#on"><code>on</code></a> method with a string
identifying the event type. Multiple handlers can now be registered
(and individually unregistered) for an event, and objects such as line
handlers now also expose events. See <a href="manual.html#events">the
full list here</a>.</p>

<p>(The <code>onKeyEvent</code> and <code>onDragEvent</code> options,
which act more as hooks than as event handlers, are still there in
their old form.)</p>

<pre data-lang="javascript">
cm.on("change", function(cm, change) {
  console.log("something changed! (" + change.origin + ")");
});
</pre>
</section>
<section id=marktext>
  <h2>markText method arguments</h2>

<p>The <a href="manual.html#markText"><code>markText</code></a> method
(which has gained some interesting new features, such as creating
atomic and read-only spans, or replacing spans with widgets) no longer
takes the CSS class name as a separate argument, but makes it an
optional field in the options object instead.</p>

<pre data-lang="javascript">
// Style first ten lines, and forbid the cursor from entering them
cm.markText({line: 0, ch: 0}, {line: 10, ch: 0}, {
  className: "magic-text",
  inclusiveLeft: true,
  atomic: true
});
</pre>
</section>
<section id=folding>
  <h2>Line folding</h2>

<p>The interface for hiding lines has been
removed. <a href="manual.html#markText"><code>markText</code></a> can
now be used to do the same in a more flexible and powerful way.</p>

<p>The <a href="../demo/folding.html">folding script</a> has been
updated to use the new interface, and should now be more robust.</p>

<pre data-lang="javascript">
// Fold a range, replacing it with the text "??"
var range = cm.markText({line: 4, ch: 2}, {line: 8, ch: 1}, {
  replacedWith: document.createTextNode("??"),
  // Auto-unfold when cursor moves into the range
  clearOnEnter: true
});
// Get notified when auto-unfolding
CodeMirror.on(range, "clear", function() {
  console.log("boom");
});
</pre>
</section>
<section id=lineclass>
  <h2>Line CSS classes</h2>

<p>The <code>setLineClass</code> method has been replaced
by <a href="manual.html#addLineClass"><code>addLineClass</code></a>
and <a href="manual.html#removeLineClass"><code>removeLineClass</code></a>,
which allow more modular control over the classes attached to a line.</p>

<pre data-lang="javascript">
var marked = cm.addLineClass(10, "background", "highlighted-line");
setTimeout(function() {
  cm.removeLineClass(marked, "background", "highlighted-line");
});
</pre>
</section>
<section id=positions>
  <h2>Position properties</h2>

<p>All methods that take or return objects that represent screen
positions now use <code>{left, top, bottom, right}</code> properties
(not always all of them) instead of the <code>{x, y, yBot}</code> used
by some methods in v2.x.</p>

<p>Affected methods
are <a href="manual.html#cursorCoords"><code>cursorCoords</code></a>, <a href="manual.html#charCoords"><code>charCoords</code></a>, <a href="manual.html#coordsChar"><code>coordsChar</code></a>,
and <a href="manual.html#getScrollInfo"><code>getScrollInfo</code></a>.</p>
</section>
<section id=matchbrackets>
  <h2>Bracket matching no longer in core</h2>

<p>The <a href="manual.html#addon_matchbrackets"><code>matchBrackets</code></a>
option is no longer defined in the core editor.
Load <code>addon/edit/matchbrackets.js</code> to enable it.</p>
</section>
<section id=modes>
  <h2>Mode management</h2>

<p>The <code>CodeMirror.listModes</code>
and <code>CodeMirror.listMIMEs</code> functions, used for listing
defined modes, are gone. You are now encouraged to simply
inspect <code>CodeMirror.modes</code> (mapping mode names to mode
constructors) and <code>CodeMirror.mimeModes</code> (mapping MIME
strings to mode specs).</p>
</section>
<section id=new>
  <h2>New features</h2>

<p>Some more reasons to upgrade to version 3.</p>

<ul>
  <li>Bi-directional text support. CodeMirror will now mostly do the
  right thing when editing Arabic or Hebrew text.</li>
  <li>Arbitrary line heights. Using fonts with different heights
  inside the editor (whether off by one pixel or fifty) is now
  supported and handled gracefully.</li>
  <li>In-line widgets. See <a href="../demo/widget.html">the demo</a>
  and <a href="manual.html#addLineWidget">the docs</a>.</li>
  <li>Defining custom options
  with <a href="manual.html#defineOption"><code>CodeMirror.defineOption</code></a>.</li>
</ul>
</section>
</article>

<script>setTimeout(function(){CodeMirror.colorize();}, 20);</script>
