<!doctype html>

<title>CodeMirror: Full Screen Editing</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<link rel="stylesheet" href="../addon/display/fullscreen.css">
<link rel="stylesheet" href="../theme/night.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/xml/xml.js"></script>
<script src="../addon/display/fullscreen.js"></script>

<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Full Screen Editing</a>
  </ul>
</div>

<article>
<h2>Full Screen Editing</h2>
<form><textarea id="code" name="code" rows="5">
  <dt id="option_indentWithTabs"><code>indentWithTabs (boolean)</code></dt>
  <dd>Whether, when indenting, the first N*8 spaces should be
  replaced by N tabs. Default is false.</dd>

  <dt id="option_tabMode"><code>tabMode (string)</code></dt>
  <dd>Determines what happens when the user presses the tab key.
  Must be one of the following:
    <dl>
      <dt><code>"classic" (the default)</code></dt>
      <dd>When nothing is selected, insert a tab. Otherwise,
      behave like the <code>"shift"</code> mode. (When shift is
      held, this behaves like the <code>"indent"</code> mode.)</dd>
      <dt><code>"shift"</code></dt>
      <dd>Indent all selected lines by
      one <a href="#option_indentUnit"><code>indentUnit</code></a>.
      If shift was held while pressing tab, un-indent all selected
      lines one unit.</dd>
      <dt><code>"indent"</code></dt>
      <dd>Indent the line the 'correctly', based on its syntactic
      context. Only works if the
      mode <a href="#indent">supports</a> it.</dd>
      <dt><code>"default"</code></dt>
      <dd>Do not capture tab presses, let the browser apply its
      default behaviour (which usually means it skips to the next
      control).</dd>
    </dl></dd>

  <dt id="option_enterMode"><code>enterMode (string)</code></dt>
  <dd>Determines whether and how new lines are indented when the
  enter key is pressed. The following modes are supported:
    <dl>
      <dt><code>"indent" (the default)</code></dt>
      <dd>Use the mode's indentation rules to give the new line
      the correct indentation.</dd>
      <dt><code>"keep"</code></dt>
      <dd>Indent the line the same as the previous line.</dd>
      <dt><code>"flat"</code></dt>
      <dd>Do not indent the new line.</dd>
    </dl></dd>

  <dt id="option_enterMode"><code>enterMode (string)</code></dt>
  <dd>Determines whether and how new lines are indented when the
  enter key is pressed. The following modes are supported:
    <dl>
      <dt><code>"indent" (the default)</code></dt>
      <dd>Use the mode's indentation rules to give the new line
      the correct indentation.</dd>
      <dt><code>"keep"</code></dt>
      <dd>Indent the line the same as the previous line.</dd>
      <dt><code>"flat"</code></dt>
      <dd>Do not indent the new line.</dd>
    </dl></dd>

  <dt id="option_enterMode"><code>enterMode (string)</code></dt>
  <dd>Determines whether and how new lines are indented when the
  enter key is pressed. The following modes are supported:
    <dl>
      <dt><code>"indent" (the default)</code></dt>
      <dd>Use the mode's indentation rules to give the new line
      the correct indentation.</dd>
      <dt><code>"keep"</code></dt>
      <dd>Indent the line the same as the previous line.</dd>
      <dt><code>"flat"</code></dt>
      <dd>Do not indent the new line.</dd>
    </dl></dd>

  <dt id="option_enterMode"><code>enterMode (string)</code></dt>
  <dd>Determines whether and how new lines are indented when the
  enter key is pressed. The following modes are supported:
    <dl>
      <dt><code>"indent" (the default)</code></dt>
      <dd>Use the mode's indentation rules to give the new line
      the correct indentation.</dd>
      <dt><code>"keep"</code></dt>
      <dd>Indent the line the same as the previous line.</dd>
      <dt><code>"flat"</code></dt>
      <dd>Do not indent the new line.</dd>
    </dl></dd>

</textarea></form>
  <script>
    var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
      lineNumbers: true,
      theme: "night",
      extraKeys: {
        "F11": function(cm) {
          cm.setOption("fullScreen", !cm.getOption("fullScreen"));
        },
        "Esc": function(cm) {
          if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
        }
      }
    });
  </script>

    <p>Demonstration of
    the <a href="../doc/manual.html#addon_fullscreen">fullscreen</a>
    addon. Press <strong>F11</strong> when cursor is in the editor to
    toggle full screen editing. <strong>Esc</strong> can also be used
    to <i>exit</i> full screen editing.</p>
  </article>
