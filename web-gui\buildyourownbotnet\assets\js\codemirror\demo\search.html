<!doctype html>

<title>CodeMirror: Search/Replace Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<link rel="stylesheet" href="../addon/dialog/dialog.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/xml/xml.js"></script>
<script src="../addon/dialog/dialog.js"></script>
<script src="../addon/search/searchcursor.js"></script>
<script src="../addon/search/search.js"></script>
<style type="text/css">
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
      dt {font-family: monospace; color: #666;}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Search/Replace</a>
  </ul>
</div>

<article>
<h2>Search/Replace Demo</h2>
<form><textarea id="code" name="code">
  <dt id="option_indentWithTabs"><code>indentWithTabs (boolean)</code></dt>
  <dd>Whether, when indenting, the first N*8 spaces should be
  replaced by N tabs. Default is false.</dd>

  <dt id="option_tabMode"><code>tabMode (string)</code></dt>
  <dd>Determines what happens when the user presses the tab key.
  Must be one of the following:
    <dl>
      <dt><code>"classic" (the default)</code></dt>
      <dd>When nothing is selected, insert a tab. Otherwise,
      behave like the <code>"shift"</code> mode. (When shift is
      held, this behaves like the <code>"indent"</code> mode.)</dd>
      <dt><code>"shift"</code></dt>
      <dd>Indent all selected lines by
      one <a href="#option_indentUnit"><code>indentUnit</code></a>.
      If shift was held while pressing tab, un-indent all selected
      lines one unit.</dd>
      <dt><code>"indent"</code></dt>
      <dd>Indent the line the 'correctly', based on its syntactic
      context. Only works if the
      mode <a href="#indent">supports</a> it.</dd>
      <dt><code>"default"</code></dt>
      <dd>Do not capture tab presses, let the browser apply its
      default behaviour (which usually means it skips to the next
      control).</dd>
    </dl></dd>

  <dt id="option_enterMode"><code>enterMode (string)</code></dt>
  <dd>Determines whether and how new lines are indented when the
  enter key is pressed. The following modes are supported:
    <dl>
      <dt><code>"indent" (the default)</code></dt>
      <dd>Use the mode's indentation rules to give the new line
      the correct indentation.</dd>
      <dt><code>"keep"</code></dt>
      <dd>Indent the line the same as the previous line.</dd>
      <dt><code>"flat"</code></dt>
      <dd>Do not indent the new line.</dd>
    </dl></dd>
</textarea></form>

    <script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {mode: "text/html", lineNumbers: true});
</script>

    <p>Demonstration of primitive search/replace functionality. The
    keybindings (which can be overridden by custom keymaps) are:</p>
    <dl>
      <dt>Ctrl-F / Cmd-F</dt><dd>Start searching</dd>
      <dt>Ctrl-G / Cmd-G</dt><dd>Find next</dd>
      <dt>Shift-Ctrl-G / Shift-Cmd-G</dt><dd>Find previous</dd>
      <dt>Shift-Ctrl-F / Cmd-Option-F</dt><dd>Replace</dd>
      <dt>Shift-Ctrl-R / Shift-Cmd-Option-F</dt><dd>Replace all</dd>
    </dl>
    <p>Searching is enabled by
    including <a href="../addon/search/search.js">addon/search/search.js</a>
    and <a href="../addon/search/searchcursor.js">addon/search/searchcursor.js</a>.
    For good-looking input dialogs, you also want to include
    <a href="../addon/dialog/dialog.js">addon/dialog/dialog.js</a>
    and <a href="../addon/dialog/dialog.css">addon/dialog/dialog.css</a>.</p>
  </article>
