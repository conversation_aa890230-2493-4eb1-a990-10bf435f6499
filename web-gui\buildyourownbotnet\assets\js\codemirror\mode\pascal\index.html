<!doctype html>

<title>CodeMirror: Pascal mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="pascal.js"></script>
<style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Pascal</a>
  </ul>
</div>

<article>
<h2>Pascal mode</h2>


<div><textarea id="code" name="code">
(* Example Pascal code *)

while a <> b do writeln('Waiting');
 
if a > b then 
  writeln('Condition met')
else 
  writeln('Condition not met');
 
for i := 1 to 10 do 
  writeln('Iteration: ', i:1);
 
repeat
  a := a + 1
until a = 10;
 
case i of
  0: write('zero');
  1: write('one');
  2: write('two')
end;
</textarea></div>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        mode: "text/x-pascal"
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-pascal</code>.</p>
  </article>
