<!doctype html>

<title>CodeMirror: Release History</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">
<script src="activebookmark.js"></script>

<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active data-default="true" href="#v3">Version 3.x</a>
    <li><a href="#v2">Version 2.x</a>
    <li><a href="#v1">Version 0.x</a>
  </ul>
</div>

<article>

<h2>Release notes and version history</h2>

<section id=v3 class=first>

  <h2>Version 3.x</h2>

  <p class="rel">21-11-2013: <a href="http://codemirror.net/codemirror-3.21.zip">Version 3.21</a>:</p>

  <ul class="rel-note">
    <li>Auto-indenting a block will no longer add trailing whitespace to blank lines.</a>
    <li>Marking text has a new option <a href="manual.html#markText"><code>clearWhenEmpty</code></a> to control auto-removal.</li>
    <li>Several bugfixes in the handling of bidirectional text.</li>
    <li>The <a href="../mode/xml/index.html">XML</a> and <a href="../mode/css/index.html">CSS</a> modes were largely rewritten. <a href="../mode/css/less.html">LESS</a> support was added to the CSS mode.</li>
    <li>The OCaml mode was moved to an <a href="../mode/mllike/index.html">mllike</a> mode, F# support added.</li>
    <li>Make it possible to fetch multiple applicable helper values with <a href="manual.html#getHelpers"><code>getHelpers</code></a>, and to register helpers matched on predicates with <a href="manual.html#registerGlobalHelper"><code>registerGlobalHelper</code></a>.</li>
    <li>New theme <a href="../demo/theme.html?pastel-on-dark">pastel-on-dark</a>.</li>
    <li>Better ECMAScript 6 support in <a href="../mode/javascript/index.html">JavaScript</a> mode.</li>
  </ul>

  <p class="rel">21-11-2013: <a href="http://codemirror.net/codemirror-3.20.zip">Version 3.20</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/julia/index.html">Julia</a> and <a href="../mode/pegjs/index.html">PEG.js</a>.</li>
    <li>Support ECMAScript 6 in the <a href="../mode/javascript/index.html">JavaScript mode</a>.</li>
    <li>Improved indentation for the <a href="../mode/coffeescript/index.html">CoffeeScript mode</a>.</li>
    <li>Make non-printable-character representation <a href="manual.html#option_specialChars">configurable</a>.</li>
    <li>Add ‘notification’ functionality to <a href="manual.html#addon_dialog">dialog</a> addon.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/3.19.0...3.20.0">list of patches</a>.</li>
  </ul>

  <p class="rel">21-10-2013: <a href="http://codemirror.net/codemirror-3.19.zip">Version 3.19</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/eiffel/index.html">Eiffel</a>, <a href="../mode/gherkin/index.html">Gherkin</a>, <a href="../mode/sql/?mime=text/x-mssql">MSSQL dialect</a>.</li>
    <li>New addons: <a href="manual.html#addon_hardwrap">hardwrap</a>, <a href="manual.html#addon_sql-hint">sql-hint</a>.</li>
    <li>New theme: <a href="../demo/theme.html?mbo">MBO</a>.</li>
    <li>Add <a href="manual.html#token_style_line">support</a> for line-level styling from mode tokenizers.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/3.18.0...3.19.0">list of patches</a>.</li>
  </ul>

  <p class="rel">23-09-2013: <a href="http://codemirror.net/codemirror-3.18.zip">Version 3.18</a>:</p>

  <p class="rel-note">Emergency release to fix a problem in 3.17
  where <code>.setOption("lineNumbers", false)</code> would raise an
  error.</p>

  <p class="rel">23-09-2013: <a href="http://codemirror.net/codemirror-3.17.zip">Version 3.17</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/fortran/index.html">Fortran</a>, <a href="../mode/octave/index.html">Octave</a> (Matlab), <a href="../mode/toml/index.html">TOML</a>, and <a href="../mode/dtd/index.html">DTD</a>.</li>
    <li>New addons: <a href="../addon/lint/css-lint.js"><code>css-lint</code></a>, <a href="manual.html#addon_css-hint"><code>css-hint</code></a>.</li>
    <li>Improve resilience to CSS 'frameworks' that globally mess up <code>box-sizing</code>.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/3.16.0...3.17.0">list of patches</a>.</li>
  </ul>

  <p class="rel">21-08-2013: <a href="http://codemirror.net/codemirror-3.16.zip">Version 3.16</a>:</p>

  <ul class="rel-note">
    <li>The whole codebase is now under a single <a href="../LICENSE">license</a> file.</li>
    <li>The project page was overhauled and redesigned.</li>
    <li>New themes: <a href="../demo/theme.html?paraiso-dark">Paraiso</a> (<a href="../demo/theme.html?paraiso-light">light</a>), <a href="../demo/theme.html?the-matrix">The Matrix</a>.</li>
    <li>Improved interaction between themes and <a href="manual.html#addon_active-line">active-line</a>/<a href="manual.html#addon_matchbrackets">matchbrackets</a> addons.</li>
    <li>New <a href="manual.html#addon_foldcode">folding</a> function <code>CodeMirror.fold.comment</code>.</li>
    <li>Added <a href="manual.html#addon_fullscreen">fullscreen</a> addon.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/3.15.0...3.16.0">list of patches</a>.</li>
  </ul>

  <p class="rel">29-07-2013: <a href="http://codemirror.net/codemirror-3.15.zip">Version 3.15</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/jade/index.html">Jade</a>, <a href="../mode/nginx/index.html">Nginx</a>.</li>
    <li>New addons: <a href="../demo/tern.html">Tern</a>, <a href="manual.html#addon_matchtags">matchtags</a>, and <a href="manual.html#addon_foldgutter">foldgutter</a>.</li>
    <li>Introduced <a href="manual.html#getHelper"><em>helper</em></a> concept (<a href="https://groups.google.com/forum/#!msg/codemirror/cOc0xvUUEUU/nLrX1-qnidgJ">context</a>).</li>
    <li>New method: <a href="manual.html#getModeAt"><code>getModeAt</code></a>.</li>
    <li>New themes: base16 <a href="../demo/theme.html?base16-dark">dark</a>/<a href="../demo/theme.html?base16-light">light</a>, 3024 <a href="../demo/theme.html?3024-night">dark</a>/<a href="../demo/theme.html?3024-day">light</a>, <a href="../demo/theme.html?tomorrow-night-eighties">tomorrow-night</a>.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/3.14.0...3.15.0">list of patches</a>.</li>
  </ul>

  <p class="rel">20-06-2013: <a href="http://codemirror.net/codemirror-3.14.zip">Version 3.14</a>:</p>

  <ul class="rel-note">
    <li>New
    addons: <a href="manual.html#addon_trailingspace">trailing
    space highlight</a>, <a href="manual.html#addon_xml-hint">XML
    completion</a> (rewritten),
    and <a href="manual.html#addon_merge">diff merging</a>.</li>
    <li><a href="manual.html#markText"><code>markText</code></a>
    and <a href="manual.html#addLineWidget"><code>addLineWidget</code></a>
    now take a <code>handleMouseEvents</code> option.</li>
    <li>New methods: <a href="manual.html#lineAtHeight"><code>lineAtHeight</code></a>,
    <a href="manual.html#getTokenTypeAt"><code>getTokenTypeAt</code></a>.</li>
    <li>More precise cleanness-tracking
    using <a href="manual.html#changeGeneration"><code>changeGeneration</code></a>
    and <a href="manual.html#isClean"><code>isClean</code></a>.</li>
    <li>Many extensions to <a href="../demo/emacs.html">Emacs</a> mode
    (prefixes, more navigation units, and more).</li>
    <li>New
    events <a href="manual.html#event_keyHandled"><code>"keyHandled"</code></a>
    and <a href="manual.html#event_inputRead"><code>"inputRead"</code></a>.</li>
    <li>Various improvements to <a href="../mode/ruby/index.html">Ruby</a>,
    <a href="../mode/smarty/index.html">Smarty</a>, <a href="../mode/sql/index.html">SQL</a>,
    and <a href="../demo/vim.html">Vim</a> modes.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/3.13.0...3.14.0">list of patches</a>.</li>
  </ul>

  <p class="rel">20-05-2013: <a href="http://codemirror.net/codemirror-3.13.zip">Version 3.13</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/cobol/index.html">COBOL</a> and <a href="../mode/haml/index.html">HAML</a>.</li>
    <li>New options: <a href="manual.html#option_cursorScrollMargin"><code>cursorScrollMargin</code></a> and <a href="manual.html#option_coverGutterNextToScrollbar"><code>coverGutterNextToScrollbar</code></a>.</li>
    <li>New addon: <a href="manual.html#addon_comment">commenting</a>.</li>
    <li>More features added to the <a href="../demo/vim.html">Vim keymap</a>.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.12...3.13.0">list of patches</a>.</li>
  </ul>

  <p class="rel">19-04-2013: <a href="http://codemirror.net/codemirror-3.12.zip">Version 3.12</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/gas/index.html">GNU assembler</a>.</li>
    <li>New
    options: <a href="manual.html#option_maxHighlightLength"><code>maxHighlightLength</code></a>
    and <a href="manual.html#option_historyEventDelay"><code>historyEventDelay</code></a>.</li>
    <li>Added <a href="manual.html#mark_addToHistory"><code>addToHistory</code></a>
    option for <code>markText</code>.</li>
    <li>Various fixes to JavaScript tokenization and indentation corner cases.</li>
    <li>Further improvements to the vim mode.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.11...v3.12">list of patches</a>.</li>
  </ul>

  <p class="rel">20-03-2013: <a href="http://codemirror.net/codemirror-3.11.zip">Version 3.11</a>:</p>

  <ul class="rel-note">
    <li><strong>Removed code:</strong> <code>collapserange</code>,
    <code>formatting</code>, and <code>simple-hint</code>
    addons. <code>plsql</code> and <code>mysql</code> modes
    (use <a href="../mode/sql/index.html"><code>sql</code></a> mode).</li>
    <li><strong>Moved code:</strong> the range-finding functions for folding now have <a href="../addon/fold/">their own files</a>.</li>
    <li><strong>Changed interface:</strong>
    the <a href="manual.html#addon_continuecomment"><code>continuecomment</code></a>
    addon now exposes an option, rather than a command.</li>
    <li>New
    modes: <a href="../mode/css/scss.html">SCSS</a>, <a href="../mode/tcl/index.html">Tcl</a>, <a href="../mode/livescript/index.html">LiveScript</a>,
    and <a href="../mode/mirc/index.html">mIRC</a>.</li>
    <li>New addons: <a href="../demo/placeholder.html"><code>placeholder</code></a>, <a href="../demo/html5complete.html">HTML completion</a>.</li>
    <li>New
    methods: <a href="manual.html#hasFocus"><code>hasFocus</code></a>, <a href="manual.html#defaultCharWidth"><code>defaultCharWidth</code></a>.</li>
    <li>New events: <a href="manual.html#event_beforeCursorEnter"><code>beforeCursorEnter</code></a>, <a href="manual.html#event_renderLine"><code>renderLine</code></a>.</li>
    <li>Many improvements to the <a href="manual.html#addon_show-hint"><code>show-hint</code></a> completion
    dialog addon.</li>
    <li>Tweak behavior of by-word cursor motion.</li>
    <li>Further improvements to the <a href="../demo/vim.html">vim mode</a>.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.1...v3.11">list of patches</a>.</li>
  </ul>

  <p class="rel">21-02-2013: <a href="http://codemirror.net/codemirror-3.1.zip">Version 3.1</a>:</p>

  <ul class="rel-note">
    <li><strong>Incompatible:</strong> key handlers may
    now <em>return</em>, rather
    than <em>throw</em> <code>CodeMirror.Pass</code> to signal they
    didn't handle the key.</li>
    <li>Make documents a <a href="manual.html#api_doc">first-class
    construct</a>, support split views and subviews.</li>
    <li>Add a <a href="manual.html#addon_show-hint">new module</a>
    for showing completion hints.
    Deprecate <code>simple-hint.js</code>.</li>
    <li>Extend <a href="../mode/htmlmixed/index.html">htmlmixed mode</a>
    to allow custom handling of script types.</li>
    <li>Support an <code>insertLeft</code> option
    to <a href="manual.html#setBookmark"><code>setBookmark</code></a>.</li>
    <li>Add an <a href="manual.html#eachLine"><code>eachLine</code></a>
    method to iterate over a document.</li>
    <li>New addon modules: <a href="../demo/markselection.html">selection
    marking</a>, <a href="../demo/lint.html">linting</a>,
    and <a href="../demo/closebrackets.html">automatic bracket
    closing</a>.</li>
    <li>Add <a href="manual.html#event_beforeChange"><code>"beforeChange"</code></a>
    and <a href="manual.html#event_beforeSelectionChange"><code>"beforeSelectionChange"</code></a>
    events.</li>
    <li>Add <a href="manual.html#event_hide"><code>"hide"</code></a>
    and <a href="manual.html#event_unhide"><code>"unhide"</code></a>
    events to marked ranges.</li>
    <li>Fix <a href="manual.html#coordsChar"><code>coordsChar</code></a>'s
    interpretation of its argument to match the documentation.</li>
    <li>New modes: <a href="../mode/turtle/index.html">Turtle</a>
    and <a href="../mode/q/index.html">Q</a>.</li>
    <li>Further improvements to the <a href="../demo/vim.html">vim mode</a>.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.01...v3.1">list of patches</a>.</li>
  </ul>
  

  <p class="rel">25-01-2013: <a href="http://codemirror.net/codemirror-3.02.zip">Version 3.02</a>:</p>

  <p class="rel-note">Single-bugfix release. Fixes a problem that
  prevents CodeMirror instances from being garbage-collected after
  they become unused.</p>

  <p class="rel">21-01-2013: <a href="http://codemirror.net/codemirror-3.01.zip">Version 3.01</a>:</p>

  <ul class="rel-note">
    <li>Move all add-ons into an organized directory structure
    under <a href="../addon/"><code>/addon</code></a>. <strong>You might have to adjust your
    paths.</strong></li>
    <li>New
    modes: <a href="../mode/d/index.html">D</a>, <a href="../mode/sass/index.html">Sass</a>, <a href="../mode/apl/index.html">APL</a>, <a href="../mode/sql/index.html">SQL</a>
    (configurable), and <a href="../mode/asterisk/index.html">Asterisk</a>.</li>
    <li>Several bugfixes in right-to-left text support.</li>
    <li>Add <a href="manual.html#option_rtlMoveVisually"><code>rtlMoveVisually</code></a> option.</li>
    <li>Improvements to vim keymap.</li>
    <li>Add built-in (lightweight) <a href="manual.html#addOverlay">overlay mode</a> support.</li>
    <li>Support <code>showIfHidden</code> option for <a href="manual.html#addLineWidget">line widgets</a>.</li>
    <li>Add simple <a href="manual.html#addon_python-hint">Python hinter</a>.</li>
    <li>Bring back the <a href="manual.html#option_fixedGutter"><code>fixedGutter</code></a> option.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.0...v3.01">list of patches</a>.</li>
  </ul>

  <p class="rel">10-12-2012: <a href="http://codemirror.net/codemirror-3.0.zip">Version 3.0</a>:</p>

  <p class="rel-note"><strong>New major version</strong>. Only
  partially backwards-compatible. See
  the <a href="upgrade_v3.html">upgrading guide</a> for more
  information. Changes since release candidate 2:</p>

  <ul class="rel-note">
    <li>Rewritten VIM mode.</li>
    <li>Fix a few minor scrolling and sizing issues.</li>
    <li>Work around Safari segfault when dragging.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.0rc2...v3.0">list of patches</a>.</li>
  </ul>
  
  <p class="rel">20-11-2012: <a href="http://codemirror.net/codemirror-3.0rc2.zip">Version 3.0, release candidate 2</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/http/index.html">HTTP</a>.</li>
    <li>Improved handling of selection anchor position.</li>
    <li>Improve IE performance on longer lines.</li>
    <li>Reduce gutter glitches during horiz. scrolling.</li>
    <li>Add <a href="manual.html#addKeyMap"><code>addKeyMap</code></a> and <a href="manual.html#removeKeyMap"><code>removeKeyMap</code></a> methods.</li>
    <li>Rewrite <code>formatting</code> and <code>closetag</code> add-ons.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.0rc1...v3.0rc2">list of patches</a>.</li>
  </ul>

  <p class="rel">20-11-2012: <a href="http://codemirror.net/codemirror-3.0rc1.zip">Version 3.0, release candidate 1</a>:</p>

  <ul class="rel-note">
    <li>New theme: <a href="../demo/theme.html?solarized%20light">Solarized</a>.</li>
    <li>Introduce <a href="manual.html#addLineClass"><code>addLineClass</code></a>
    and <a href="manual.html#removeLineClass"><code>removeLineClass</code></a>,
    drop <code>setLineClass</code>.</li>
    <li>Add a <em>lot</em> of
    new <a href="manual.html#markText">options for marked text</a>
    (read-only, atomic, collapsed, widget replacement).</li>
    <li>Remove the old code folding interface in favour of these new ranges.</li>
    <li>Add <a href="manual.html#isClean"><code>isClean</code></a>/<a href="manual.html#markClean"><code>markClean</code></a> methods.</li>
    <li>Remove <code>compoundChange</code> method, use better undo-event-combining heuristic.</li>
    <li>Improve scrolling performance smoothness.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.0beta2...v3.0rc1">list of patches</a>.</li>
  </ul>

  <p class="rel">22-10-2012: <a href="http://codemirror.net/codemirror-3.0beta2.zip">Version 3.0, beta 2</a>:</p>

  <ul class="rel-note">
    <li>Fix page-based coordinate computation.</li>
    <li>Fix firing of <a href="manual.html#event_gutterClick"><code>gutterClick</code></a> event.</li>
    <li>Add <a href="manual.html#option_cursorHeight"><code>cursorHeight</code></a> option.</li>
    <li>Fix bi-directional text regression.</li>
    <li>Add <a href="manual.html#option_viewportMargin"><code>viewportMargin</code></a> option.</li>
    <li>Directly handle mousewheel events (again, hopefully better).</li>
    <li>Make vertical cursor movement more robust (through widgets, big line gaps).</li>
    <li>Add <a href="manual.html#option_flattenSpans"><code>flattenSpans</code></a> option.</li>
    <li>Many optimizations. Poor responsiveness should be fixed.</li>
    <li>Initialization in hidden state works again.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v3.0beta1...v3.0beta2">list of patches</a>.</li>
  </ul>

  <p class="rel">19-09-2012: <a href="http://codemirror.net/codemirror-3.0beta1.zip">Version 3.0, beta 1</a>:</p>

  <ul class="rel-note">
    <li>Bi-directional text support.</li>
    <li>More powerful gutter model.</li>
    <li>Support for arbitrary text/widget height.</li>
    <li>In-line widgets.</li>
    <li>Generalized event handling.</li>
  </ul>

</section>

<section id=v2>

  <h2>Version 2.x</h2>

  <p class="rel">21-01-2013: <a href="http://codemirror.net/codemirror-2.38.zip">Version 2.38</a>:</p>

  <p class="rel-note">Integrate some bugfixes, enhancements to the vim keymap, and new
  modes
  (<a href="../mode/d/index.html">D</a>, <a href="../mode/sass/index.html">Sass</a>, <a href="../mode/apl/index.html">APL</a>)
  from the v3 branch.</p>

  <p class="rel">20-12-2012: <a href="http://codemirror.net/codemirror-2.37.zip">Version 2.37</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/sql/index.html">SQL</a> (will replace <a href="../mode/plsql/index.html">plsql</a> and <a href="../mode/mysql/index.html">mysql</a> modes).</li>
    <li>Further work on the new VIM mode.</li>
    <li>Fix Cmd/Ctrl keys on recent Operas on OS X.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v2.36...v2.37">list of patches</a>.</li>
  </ul>

  <p class="rel">20-11-2012: <a href="http://codemirror.net/codemirror-2.36.zip">Version 2.36</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/z80/index.html">Z80 assembly</a>.</li>
    <li>New theme: <a href="../demo/theme.html?twilight">Twilight</a>.</li>
    <li>Add command-line compression helper.</li>
    <li>Make <a href="manual.html#scrollIntoView"><code>scrollIntoView</code></a> public.</li>
    <li>Add <a href="manual.html#defaultTextHeight"><code>defaultTextHeight</code></a> method.</li>
    <li>Various extensions to the vim keymap.</li>
    <li>Make <a href="../mode/php/index.html">PHP mode</a> build on <a href="../mode/htmlmixed/index.html">mixed HTML mode</a>.</li>
    <li>Add <a href="manual.html#addon_continuecomment">comment-continuing</a> add-on.</li>
    <li>Full <a href="../https://github.com/marijnh/CodeMirror/compare/v2.35...v2.36">list of patches</a>.</li>
  </ul>

  <p class="rel">22-10-2012: <a href="http://codemirror.net/codemirror-2.35.zip">Version 2.35</a>:</p>

  <ul class="rel-note">
    <li>New (sub) mode: <a href="../mode/javascript/typescript.html">TypeScript</a>.</li>
    <li>Don't overwrite (insert key) when pasting.</li>
    <li>Fix several bugs in <a href="manual.html#markText"><code>markText</code></a>/undo interaction.</li>
    <li>Better indentation of JavaScript code without semicolons.</li>
    <li>Add <a href="manual.html#defineInitHook"><code>defineInitHook</code></a> function.</li>
    <li>Full <a href="https://github.com/marijnh/CodeMirror/compare/v2.34...v2.35">list of patches</a>.</li>
  </ul>

  <p class="rel">19-09-2012: <a href="http://codemirror.net/codemirror-2.34.zip">Version 2.34</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/commonlisp/index.html">Common Lisp</a>.</li>
    <li>Fix right-click select-all on most browsers.</li>
    <li>Change the way highlighting happens:<br>&nbsp; Saves memory and CPU cycles.<br>&nbsp; <code>compareStates</code> is no longer needed.<br>&nbsp; <code>onHighlightComplete</code> no longer works.</li>
    <li>Integrate mode (Markdown, XQuery, CSS, sTex) tests in central testsuite.</li>
    <li>Add a <a href="manual.html#version"><code>CodeMirror.version</code></a> property.</li>
    <li>More robust handling of nested modes in <a href="../demo/formatting.html">formatting</a> and <a href="../demo/closetag.html">closetag</a> plug-ins.</li>
    <li>Un/redo now preserves <a href="manual.html#markText">marked text</a> and bookmarks.</li>
    <li><a href="https://github.com/marijnh/CodeMirror/compare/v2.33...v2.34">Full list</a> of patches.</li>
  </ul>

  <p class="rel">23-08-2012: <a href="http://codemirror.net/codemirror-2.33.zip">Version 2.33</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/sieve/index.html">Sieve</a>.</li>
    <li>New <a href="manual.html#getViewport"><code>getViewPort</code></a> and <a href="manual.html#option_onViewportChange"><code>onViewportChange</code></a> API.</li>
    <li><a href="manual.html#option_cursorBlinkRate">Configurable</a> cursor blink rate.</li>
    <li>Make binding a key to <code>false</code> disabling handling (again).</li>
    <li>Show non-printing characters as red dots.</li>
    <li>More tweaks to the scrolling model.</li>
    <li>Expanded testsuite. Basic linter added.</li>
    <li>Remove most uses of <code>innerHTML</code>. Remove <code>CodeMirror.htmlEscape</code>.</li>
    <li><a href="https://github.com/marijnh/CodeMirror/compare/v2.32...v2.33">Full list</a> of patches.</li>
  </ul>

  <p class="rel">23-07-2012: <a href="http://codemirror.net/codemirror-2.32.zip">Version 2.32</a>:</p>

  <p class="rel-note">Emergency fix for a bug where an editor with
  line wrapping on IE will break when there is <em>no</em>
  scrollbar.</p>

  <p class="rel">20-07-2012: <a href="http://codemirror.net/codemirror-2.31.zip">Version 2.31</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/ocaml/index.html">OCaml</a>, <a href="../mode/haxe/index.html">Haxe</a>, and <a href="../mode/vb/index.html">VB.NET</a>.</li>
    <li>Several fixes to the new scrolling model.</li>
    <li>Add a <a href="manual.html#setSize"><code>setSize</code></a> method for programmatic resizing.</li>
    <li>Add <a href="manual.html#getHistory"><code>getHistory</code></a> and <a href="manual.html#setHistory"><code>setHistory</code></a> methods.</li>
    <li>Allow custom line separator string in <a href="manual.html#getValue"><code>getValue</code></a> and <a href="manual.html#getRange"><code>getRange</code></a>.</li>
    <li>Support double- and triple-click drag, double-clicking whitespace.</li>
    <li>And more... <a href="https://github.com/marijnh/CodeMirror/compare/v2.3...v2.31">(all patches)</a></li>
  </ul>

  <p class="rel">22-06-2012: <a href="http://codemirror.net/codemirror-2.3.zip">Version 2.3</a>:</p>

  <ul class="rel-note">
    <li><strong>New scrollbar implementation</strong>. Should flicker less. Changes DOM structure of the editor.</li>
    <li>New theme: <a href="../demo/theme.html?vibrant-ink">vibrant-ink</a>.</li>
    <li>Many extensions to the VIM keymap (including text objects).</li>
    <li>Add <a href="../demo/multiplex.html">mode-multiplexing</a> utility script.</li>
    <li>Fix bug where right-click paste works in read-only mode.</li>
    <li>Add a <a href="manual.html#getScrollInfo"><code>getScrollInfo</code></a> method.</li>
    <li>Lots of other <a href="https://github.com/marijnh/CodeMirror/compare/v2.25...v2.3">fixes</a>.</li>
  </ul>

  <p class="rel">23-05-2012: <a href="http://codemirror.net/codemirror-2.25.zip">Version 2.25</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/erlang/index.html">Erlang</a>.</li>
    <li><strong>Remove xmlpure mode</strong> (use <a href="../mode/xml/index.html">xml.js</a>).</li>
    <li>Fix line-wrapping in Opera.</li>
    <li>Fix X Windows middle-click paste in Chrome.</li>
    <li>Fix bug that broke pasting of huge documents.</li>
    <li>Fix backspace and tab key repeat in Opera.</li>
  </ul>

  <p class="rel">23-04-2012: <a href="http://codemirror.net/codemirror-2.24.zip">Version 2.24</a>:</p>

  <ul class="rel-note">
    <li><strong>Drop support for Internet Explorer 6</strong>.</li>
    <li>New
    modes: <a href="../mode/shell/index.html">Shell</a>, <a href="../mode/tiki/index.html">Tiki
    wiki</a>, <a href="../mode/pig/index.html">Pig Latin</a>.</li>
    <li>New themes: <a href="../demo/theme.html?ambiance">Ambiance</a>, <a href="../demo/theme.html?blackboard">Blackboard</a>.</li>
    <li>More control over drag/drop
    with <a href="manual.html#option_dragDrop"><code>dragDrop</code></a>
    and <a href="manual.html#option_onDragEvent"><code>onDragEvent</code></a>
    options.</li>
    <li>Make HTML mode a bit less pedantic.</li>
    <li>Add <a href="manual.html#compoundChange"><code>compoundChange</code></a> API method.</li>
    <li>Several fixes in undo history and line hiding.</li>
    <li>Remove (broken) support for <code>catchall</code> in key maps,
    add <code>nofallthrough</code> boolean field instead.</li>
  </ul>

  <p class="rel">26-03-2012: <a href="http://codemirror.net/codemirror-2.23.zip">Version 2.23</a>:</p>

  <ul class="rel-note">
    <li>Change <strong>default binding for tab</strong> <a href="javascript:void(document.getElementById('tabbinding').style.display='')">[more]</a>
      <div style="display: none" id=tabbinding>
        Starting in 2.23, these bindings are default:
        <ul><li>Tab: Insert tab character</li>
          <li>Shift-tab: Reset line indentation to default</li>
          <li>Ctrl/Cmd-[: Reduce line indentation (old tab behaviour)</li>
          <li>Ctrl/Cmd-]: Increase line indentation (old shift-tab behaviour)</li>
        </ul>
      </div>
    </li>
    <li>New modes: <a href="../mode/xquery/index.html">XQuery</a> and <a href="../mode/vbscript/index.html">VBScript</a>.</li>
    <li>Two new themes: <a href="../mode/less/index.html">lesser-dark</a> and <a href="../mode/xquery/index.html">xq-dark</a>.</li>
    <li>Differentiate between background and text styles in <a href="manual.html#setLineClass"><code>setLineClass</code></a>.</li>
    <li>Fix drag-and-drop in IE9+.</li>
    <li>Extend <a href="manual.html#charCoords"><code>charCoords</code></a>
    and <a href="manual.html#cursorCoords"><code>cursorCoords</code></a> with a <code>mode</code> argument.</li>
    <li>Add <a href="manual.html#option_autofocus"><code>autofocus</code></a> option.</li>
    <li>Add <a href="manual.html#findMarksAt"><code>findMarksAt</code></a> method.</li>
  </ul>

  <p class="rel">27-02-2012: <a href="http://codemirror.net/codemirror-2.22.zip">Version 2.22</a>:</p>

  <ul class="rel-note">
    <li>Allow <a href="manual.html#keymaps">key handlers</a> to pass up events, allow binding characters.</li>
    <li>Add <a href="manual.html#option_autoClearEmptyLines"><code>autoClearEmptyLines</code></a> option.</li>
    <li>Properly use tab stops when rendering tabs.</li>
    <li>Make PHP mode more robust.</li>
    <li>Support indentation blocks in <a href="manual.html#addon_foldcode">code folder</a>.</li>
    <li>Add a script for <a href="manual.html#addon_match-highlighter">highlighting instances of the selection</a>.</li>
    <li>New <a href="../mode/properties/index.html">.properties</a> mode.</li>
    <li>Fix many bugs.</li>
  </ul>

  <p class="rel">27-01-2012: <a href="http://codemirror.net/codemirror-2.21.zip">Version 2.21</a>:</p>

  <ul class="rel-note">
    <li>Added <a href="../mode/less/index.html">LESS</a>, <a href="../mode/mysql/index.html">MySQL</a>,
    <a href="../mode/go/index.html">Go</a>, and <a href="../mode/verilog/index.html">Verilog</a> modes.</li>
    <li>Add <a href="manual.html#option_smartIndent"><code>smartIndent</code></a>
    option.</li>
    <li>Support a cursor in <a href="manual.html#option_readOnly"><code>readOnly</code></a>-mode.</li>
    <li>Support assigning multiple styles to a token.</li>
    <li>Use a new approach to drawing the selection.</li>
    <li>Add <a href="manual.html#scrollTo"><code>scrollTo</code></a> method.</li>
    <li>Allow undo/redo events to span non-adjacent lines.</li>
    <li>Lots and lots of bugfixes.</li>
  </ul>

  <p class="rel">20-12-2011: <a href="http://codemirror.net/codemirror-2.2.zip">Version 2.2</a>:</p>

  <ul class="rel-note">
    <li>Slightly incompatible API changes. Read <a href="upgrade_v2.2.html">this</a>.</li>
    <li>New approach
    to <a href="manual.html#option_extraKeys">binding</a> keys,
    support for <a href="manual.html#option_keyMap">custom
    bindings</a>.</li>
    <li>Support for overwrite (insert).</li>
    <li><a href="manual.html#option_tabSize">Custom-width</a>
    and <a href="../demo/visibletabs.html">stylable</a> tabs.</li>
    <li>Moved more code into <a href="manual.html#addons">add-on scripts</a>.</li>
    <li>Support for sane vertical cursor movement in wrapped lines.</li>
    <li>More reliable handling of
    editing <a href="manual.html#markText">marked text</a>.</li>
    <li>Add minimal <a href="../demo/emacs.html">emacs</a>
    and <a href="../demo/vim.html">vim</a> bindings.</li>
    <li>Rename <code>coordsFromIndex</code>
    to <a href="manual.html#posFromIndex"><code>posFromIndex</code></a>,
    add <a href="manual.html#indexFromPos"><code>indexFromPos</code></a>
    method.</li>
  </ul>

  <p class="rel">21-11-2011: <a href="http://codemirror.net/codemirror-2.18.zip">Version 2.18</a>:</p>
  <p class="rel-note">Fixes <code>TextMarker.clear</code>, which is broken in 2.17.</p>

  <p class="rel">21-11-2011: <a href="http://codemirror.net/codemirror-2.17.zip">Version 2.17</a>:</p>
  <ul class="rel-note">
    <li>Add support for <a href="manual.html#option_lineWrapping">line
    wrapping</a> and <a href="manual.html#hideLine">code
    folding</a>.</li>
    <li>Add <a href="../mode/gfm/index.html">Github-style Markdown</a> mode.</li>
    <li>Add <a href="../theme/monokai.css">Monokai</a>
    and <a href="../theme/rubyblue.css">Rubyblue</a> themes.</li>
    <li>Add <a href="manual.html#setBookmark"><code>setBookmark</code></a> method.</li>
    <li>Move some of the demo code into reusable components
    under <a href="../addon/"><code>lib/util</code></a>.</li>
    <li>Make screen-coord-finding code faster and more reliable.</li>
    <li>Fix drag-and-drop in Firefox.</li>
    <li>Improve support for IME.</li>
    <li>Speed up content rendering.</li>
    <li>Fix browser's built-in search in Webkit.</li>
    <li>Make double- and triple-click work in IE.</li>
    <li>Various fixes to modes.</li>
  </ul>

  <p class="rel">27-10-2011: <a href="http://codemirror.net/codemirror-2.16.zip">Version 2.16</a>:</p>
  <ul class="rel-note">
    <li>Add <a href="../mode/perl/index.html">Perl</a>, <a href="../mode/rust/index.html">Rust</a>, <a href="../mode/tiddlywiki/index.html">TiddlyWiki</a>, and <a href="../mode/groovy/index.html">Groovy</a> modes.</li>
    <li>Dragging text inside the editor now moves, rather than copies.</li>
    <li>Add a <a href="manual.html#coordsFromIndex"><code>coordsFromIndex</code></a> method.</li>
    <li><strong>API change</strong>: <code>setValue</code> now no longer clears history. Use <a href="manual.html#clearHistory"><code>clearHistory</code></a> for that.</li>
    <li><strong>API change</strong>: <a href="manual.html#markText"><code>markText</code></a> now
    returns an object with <code>clear</code> and <code>find</code>
    methods. Marked text is now more robust when edited.</li>
    <li>Fix editing code with tabs in Internet Explorer.</li>
  </ul>

  <p class="rel">26-09-2011: <a href="http://codemirror.net/codemirror-2.15.zip">Version 2.15</a>:</p>
  <p class="rel-note">Fix bug that snuck into 2.14: Clicking the
  character that currently has the cursor didn't re-focus the
  editor.</p>

  <p class="rel">26-09-2011: <a href="http://codemirror.net/codemirror-2.14.zip">Version 2.14</a>:</p>
  <ul class="rel-note">
    <li>Add <a href="../mode/clojure/index.html">Clojure</a>, <a href="../mode/pascal/index.html">Pascal</a>, <a href="../mode/ntriples/index.html">NTriples</a>, <a href="../mode/jinja2/index.html">Jinja2</a>, and <a href="../mode/markdown/index.html">Markdown</a> modes.</li>
    <li>Add <a href="../theme/cobalt.css">Cobalt</a> and <a href="../theme/eclipse.css">Eclipse</a> themes.</li>
    <li>Add a <a href="manual.html#option_fixedGutter"><code>fixedGutter</code></a> option.</li>
    <li>Fix bug with <code>setValue</code> breaking cursor movement.</li>
    <li>Make gutter updates much more efficient.</li>
    <li>Allow dragging of text out of the editor (on modern browsers).</li>
  </ul>


  <p class="rel">23-08-2011: <a href="http://codemirror.net/codemirror-2.13.zip">Version 2.13</a>:</p>
  <ul class="rel-note">
    <li>Add <a href="../mode/ruby/index.html">Ruby</a>, <a href="../mode/r/index.html">R</a>, <a href="../mode/coffeescript/index.html">CoffeeScript</a>, and <a href="../mode/velocity/index.html">Velocity</a> modes.</li>
    <li>Add <a href="manual.html#getGutterElement"><code>getGutterElement</code></a> to API.</li>
    <li>Several fixes to scrolling and positioning.</li>
    <li>Add <a href="manual.html#option_smartHome"><code>smartHome</code></a> option.</li>
    <li>Add an experimental <a href="../mode/xmlpure/index.html">pure XML</a> mode.</li>
  </ul>

  <p class="rel">25-07-2011: <a href="http://codemirror.net/codemirror-2.12.zip">Version 2.12</a>:</p>
  <ul class="rel-note">
    <li>Add a <a href="../mode/sparql/index.html">SPARQL</a> mode.</li>
    <li>Fix bug with cursor jumping around in an unfocused editor in IE.</li>
    <li>Allow key and mouse events to bubble out of the editor. Ignore widget clicks.</li>
    <li>Solve cursor flakiness after undo/redo.</li>
    <li>Fix block-reindent ignoring the last few lines.</li>
    <li>Fix parsing of multi-line attrs in XML mode.</li>
    <li>Use <code>innerHTML</code> for HTML-escaping.</li>
    <li>Some fixes to indentation in C-like mode.</li>
    <li>Shrink horiz scrollbars when long lines removed.</li>
    <li>Fix width feedback loop bug that caused the width of an inner DIV to shrink.</li>
  </ul>

  <p class="rel">04-07-2011: <a href="http://codemirror.net/codemirror-2.11.zip">Version 2.11</a>:</p>
  <ul class="rel-note">
    <li>Add a <a href="../mode/scheme/index.html">Scheme mode</a>.</li>
    <li>Add a <code>replace</code> method to search cursors, for cursor-preserving replacements.</li>
    <li>Make the <a href="../mode/clike/index.html">C-like mode</a> mode more customizable.</li>
    <li>Update XML mode to spot mismatched tags.</li>
    <li>Add <code>getStateAfter</code> API and <code>compareState</code> mode API methods for finer-grained mode magic.</li>
    <li>Add a <code>getScrollerElement</code> API method to manipulate the scrolling DIV.</li>
    <li>Fix drag-and-drop for Firefox.</li>
    <li>Add a C# configuration for the <a href="../mode/clike/index.html">C-like mode</a>.</li>
    <li>Add <a href="../demo/fullscreen.html">full-screen editing</a> and <a href="../demo/changemode.html">mode-changing</a> demos.</li>
  </ul>

  <p class="rel">07-06-2011: <a href="http://codemirror.net/codemirror-2.1.zip">Version 2.1</a>:</p>
  <p class="rel-note">Add
  a <a href="manual.html#option_theme">theme</a> system
  (<a href="../demo/theme.html">demo</a>). Note that this is not
  backwards-compatible—you'll have to update your styles and
  modes!</p>

  <p class="rel">07-06-2011: <a href="http://codemirror.net/codemirror-2.02.zip">Version 2.02</a>:</p>
  <ul class="rel-note">
    <li>Add a <a href="../mode/lua/index.html">Lua mode</a>.</li>
    <li>Fix reverse-searching for a regexp.</li>
    <li>Empty lines can no longer break highlighting.</li>
    <li>Rework scrolling model (the outer wrapper no longer does the scrolling).</li>
    <li>Solve horizontal jittering on long lines.</li>
    <li>Add <a href="../demo/runmode.html">runmode.js</a>.</li>
    <li>Immediately re-highlight text when typing.</li>
    <li>Fix problem with 'sticking' horizontal scrollbar.</li>
  </ul>

  <p class="rel">26-05-2011: <a href="http://codemirror.net/codemirror-2.01.zip">Version 2.01</a>:</p>
  <ul class="rel-note">
    <li>Add a <a href="../mode/smalltalk/index.html">Smalltalk mode</a>.</li>
    <li>Add a <a href="../mode/rst/index.html">reStructuredText mode</a>.</li>
    <li>Add a <a href="../mode/python/index.html">Python mode</a>.</li>
    <li>Add a <a href="../mode/plsql/index.html">PL/SQL mode</a>.</li>
    <li><code>coordsChar</code> now works</li>
    <li>Fix a problem where <code>onCursorActivity</code> interfered with <code>onChange</code>.</li>
    <li>Fix a number of scrolling and mouse-click-position glitches.</li>
    <li>Pass information about the changed lines to <code>onChange</code>.</li>
    <li>Support cmd-up/down on OS X.</li>
    <li>Add triple-click line selection.</li>
    <li>Don't handle shift when changing the selection through the API.</li>
    <li>Support <code>"nocursor"</code> mode for <code>readOnly</code> option.</li>
    <li>Add an <code>onHighlightComplete</code> option.</li>
    <li>Fix the context menu for Firefox.</li>
  </ul>

  <p class="rel">28-03-2011: <a href="http://codemirror.net/codemirror-2.0.zip">Version 2.0</a>:</p>
  <p class="rel-note">CodeMirror 2 is a complete rewrite that's
  faster, smaller, simpler to use, and less dependent on browser
  quirks. See <a href="internals.html">this</a>
  and <a href="http://groups.google.com/group/codemirror/browse_thread/thread/5a8e894024a9f580">this</a>
  for more information.</p>

  <p class="rel">22-02-2011: <a href="https://github.com/marijnh/codemirror/tree/beta2">Version 2.0 beta 2</a>:</p>
  <p class="rel-note">Somewhat more mature API, lots of bugs shaken out.</p>

  <p class="rel">17-02-2011: <a href="http://codemirror.net/codemirror-0.94.zip">Version 0.94</a>:</p>
  <ul class="rel-note">
    <li><code>tabMode: "spaces"</code> was modified slightly (now indents when something is selected).</li>
    <li>Fixes a bug that would cause the selection code to break on some IE versions.</li>
    <li>Disabling spell-check on WebKit browsers now works.</li>
  </ul>

  <p class="rel">08-02-2011: <a href="http://codemirror.net/">Version 2.0 beta 1</a>:</p>
  <p class="rel-note">CodeMirror 2 is a complete rewrite of
  CodeMirror, no longer depending on an editable frame.</p>

  <p class="rel">19-01-2011: <a href="http://codemirror.net/codemirror-0.93.zip">Version 0.93</a>:</p>
  <ul class="rel-note">
    <li>Added a <a href="contrib/regex/index.html">Regular Expression</a> parser.</li>
    <li>Fixes to the PHP parser.</li>
    <li>Support for regular expression in search/replace.</li>
    <li>Add <code>save</code> method to instances created with <code>fromTextArea</code>.</li>
    <li>Add support for MS T-SQL in the SQL parser.</li>
    <li>Support use of CSS classes for highlighting brackets.</li>
    <li>Fix yet another hang with line-numbering in hidden editors.</li>
  </ul>
</section>

<section id=v1>

  <h2>Version 0.x</h2>

  <p class="rel">28-03-2011: <a href="http://codemirror.net/codemirror-1.0.zip">Version 1.0</a>:</p>
  <ul class="rel-note">
    <li>Fix error when debug history overflows.</li>
    <li>Refine handling of C# verbatim strings.</li>
    <li>Fix some issues with JavaScript indentation.</li>
  </ul>

  <p class="rel">17-12-2010: <a href="http://codemirror.net/codemirror-0.92.zip">Version 0.92</a>:</p>
  <ul class="rel-note">
    <li>Make CodeMirror work in XHTML documents.</li>
    <li>Fix bug in handling of backslashes in Python strings.</li>
    <li>The <code>styleNumbers</code> option is now officially
    supported and documented.</li>
    <li><code>onLineNumberClick</code> option added.</li>
    <li>More consistent names <code>onLoad</code> and
    <code>onCursorActivity</code> callbacks. Old names still work, but
    are deprecated.</li>
    <li>Add a <a href="contrib/freemarker/index.html">Freemarker</a> mode.</li>
  </ul>

  <p class="rel">11-11-2010: <a
  href="http://codemirror.net/codemirror-0.91.zip">Version 0.91</a>:</p>
  <ul class="rel-note">
    <li>Adds support for <a href="contrib/java">Java</a>.</li>
    <li>Small additions to the <a href="contrib/php">PHP</a> and <a href="contrib/sql">SQL</a> parsers.</li>
    <li>Work around various <a href="https://bugs.webkit.org/show_bug.cgi?id=47806">Webkit</a> <a href="https://bugs.webkit.org/show_bug.cgi?id=23474">issues</a>.</li>
    <li>Fix <code>toTextArea</code> to update the code in the textarea.</li>
    <li>Add a <code>noScriptCaching</code> option (hack to ease development).</li>
    <li>Make sub-modes of <a href="mixedtest.html">HTML mixed</a> mode configurable.</li>
  </ul>

  <p class="rel">02-10-2010: <a
  href="http://codemirror.net/codemirror-0.9.zip">Version 0.9</a>:</p>
  <ul class="rel-note">
    <li>Add support for searching backwards.</li>
    <li>There are now parsers for <a href="contrib/scheme/index.html">Scheme</a>, <a href="contrib/xquery/index.html">XQuery</a>, and <a href="contrib/ometa/index.html">OmetaJS</a>.</li>
    <li>Makes <code>height: "dynamic"</code> more robust.</li>
    <li>Fixes bug where paste did not work on OS X.</li>
    <li>Add a <code>enterMode</code> and <code>electricChars</code> options to make indentation even more customizable.</li>
    <li>Add <code>firstLineNumber</code> option.</li>
    <li>Fix bad handling of <code>@media</code> rules by the CSS parser.</li>
    <li>Take a new, more robust approach to working around the invisible-last-line bug in WebKit.</li>
  </ul>

  <p class="rel">22-07-2010: <a
  href="http://codemirror.net/codemirror-0.8.zip">Version 0.8</a>:</p>
  <ul class="rel-note">
    <li>Add a <code>cursorCoords</code> method to find the screen
    coordinates of the cursor.</li>
    <li>A number of fixes and support for more syntax in the PHP parser.</li>
    <li>Fix indentation problem with JSON-mode JS parser in Webkit.</li>
    <li>Add a <a href="compress.html">minification</a> UI.</li>
    <li>Support a <code>height: dynamic</code> mode, where the editor's
    height will adjust to the size of its content.</li>
    <li>Better support for IME input mode.</li>
    <li>Fix JavaScript parser getting confused when seeing a no-argument
    function call.</li>
    <li>Have CSS parser see the difference between selectors and other
    identifiers.</li>
    <li>Fix scrolling bug when pasting in a horizontally-scrolled
    editor.</li>
    <li>Support <code>toTextArea</code> method in instances created with
    <code>fromTextArea</code>.</li>
    <li>Work around new Opera cursor bug that causes the cursor to jump
    when pressing backspace at the end of a line.</li>
  </ul>

  <p class="rel">27-04-2010: <a
  href="http://codemirror.net/codemirror-0.67.zip">Version
  0.67</a>:</p>
  <p class="rel-note">More consistent page-up/page-down behaviour
  across browsers. Fix some issues with hidden editors looping forever
  when line-numbers were enabled. Make PHP parser parse
  <code>"\\"</code> correctly. Have <code>jumpToLine</code> work on
  line handles, and add <code>cursorLine</code> function to fetch the
  line handle where the cursor currently is. Add new
  <code>setStylesheet</code> function to switch style-sheets in a
  running editor.</p>

  <p class="rel">01-03-2010: <a
  href="http://codemirror.net/codemirror-0.66.zip">Version
  0.66</a>:</p>
  <p class="rel-note">Adds <code>removeLine</code> method to API.
  Introduces the <a href="contrib/plsql/index.html">PLSQL parser</a>.
  Marks XML errors by adding (rather than replacing) a CSS class, so
  that they can be disabled by modifying their style. Fixes several
  selection bugs, and a number of small glitches.</p>

  <p class="rel">12-11-2009: <a
  href="http://codemirror.net/codemirror-0.65.zip">Version
  0.65</a>:</p>
  <p class="rel-note">Add support for having both line-wrapping and
  line-numbers turned on, make paren-highlighting style customisable
  (<code>markParen</code> and <code>unmarkParen</code> config
  options), work around a selection bug that Opera
  <em>re</em>introduced in version 10.</p>

  <p class="rel">23-10-2009: <a
  href="http://codemirror.net/codemirror-0.64.zip">Version
  0.64</a>:</p>
  <p class="rel-note">Solves some issues introduced by the
  paste-handling changes from the previous release. Adds
  <code>setSpellcheck</code>, <code>setTextWrapping</code>,
  <code>setIndentUnit</code>, <code>setUndoDepth</code>,
  <code>setTabMode</code>, and <code>setLineNumbers</code> to
  customise a running editor. Introduces an <a
  href="contrib/sql/index.html">SQL</a> parser. Fixes a few small
  problems in the <a href="contrib/python/index.html">Python</a>
  parser. And, as usual, add workarounds for various newly discovered
  browser incompatibilities.</p>

  <p class="rel"><em>31-08-2009</em>: <a href="http://codemirror.net/codemirror-0.63.zip">Version 0.63</a>:</p>
  <p class="rel-note"> Overhaul of paste-handling (less fragile), fixes for several
  serious IE8 issues (cursor jumping, end-of-document bugs) and a number
  of small problems.</p>

  <p class="rel"><em>30-05-2009</em>: <a href="http://codemirror.net/codemirror-0.62.zip">Version 0.62</a>:</p>
  <p class="rel-note">Introduces <a href="contrib/python/index.html">Python</a>
  and <a href="contrib/lua/index.html">Lua</a> parsers. Add
  <code>setParser</code> (on-the-fly mode changing) and
  <code>clearHistory</code> methods. Make parsing passes time-based
  instead of lines-based (see the <code>passTime</code> option).</p>

</section>
</article>
