<!doctype html>

<title>CodeMirror: Jinja2 mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="jinja2.js"></script>
<style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Jinja2</a>
  </ul>
</div>

<article>
<h2>Jinja2 mode</h2>
<form><textarea id="code" name="code">
&lt;html style="color: green"&gt;
  &lt;!-- this is a comment --&gt;
  &lt;head&gt;
    &lt;title&gt;Jinja2 Example&lt;/title&gt;
  &lt;/head&gt;
  &lt;body&gt;
    &lt;ul&gt;
    {# this is a comment #}
    {%- for item in li -%}
      &lt;li&gt;
        {{ item.label }}
      &lt;/li&gt;
    {% endfor -%}
    &lt;/ul&gt;
  &lt;/body&gt;
&lt;/html&gt;
</textarea></form>
    <script>
      var editor =
      CodeMirror.fromTextArea(document.getElementById("code"), {mode:
        {name: "jinja2", htmlMode: true}});
    </script>
  </article>
