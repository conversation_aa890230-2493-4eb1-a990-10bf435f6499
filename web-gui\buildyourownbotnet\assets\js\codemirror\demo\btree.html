<!doctype html>

<title>CodeMirror: B-Tree visualization</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<style type="text/css">
      .lineblock { display: inline-block; margin: 1px; height: 5px; }
      .CodeMirror {border: 1px solid #aaa; height: 400px}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">B-Tree visualization</a>
  </ul>
</div>

<article>
<h2>B-Tree visualization</h2>
<form><textarea id="code" name="code">type here, see a summary of the document b-tree below</textarea></form>
      </div>
      <div style="display: inline-block; height: 402px; overflow-y: auto" id="output"></div>
    </div>

    <script id="me">
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  lineNumbers: true,
  lineWrapping: true
});
var updateTimeout;
editor.on("change", function(cm) {
  clearTimeout(updateTimeout);
  updateTimeout = setTimeout(updateVisual, 200);
});
updateVisual();

function updateVisual() {
  var out = document.getElementById("output");
  out.innerHTML = "";

  function drawTree(out, node) {
    if (node.lines) {
      out.appendChild(document.createElement("div")).innerHTML =
        "<b>leaf</b>: " + node.lines.length + " lines, " + Math.round(node.height) + " px";
      var lines = out.appendChild(document.createElement("div"));
      lines.style.lineHeight = "6px"; lines.style.marginLeft = "10px";
      for (var i = 0; i < node.lines.length; ++i) {
        var line = node.lines[i], lineElt = lines.appendChild(document.createElement("div"));
        lineElt.className = "lineblock";
        var gray = Math.min(line.text.length * 3, 230), col = gray.toString(16);
        if (col.length == 1) col = "0" + col;
        lineElt.style.background = "#" + col + col + col;
                          console.log(line.height, line);
        lineElt.style.width = Math.max(Math.round(line.height / 3), 1) + "px";
      }
    } else {
      out.appendChild(document.createElement("div")).innerHTML =
        "<b>node</b>: " + node.size + " lines, " + Math.round(node.height) + " px";
      var sub = out.appendChild(document.createElement("div"));
      sub.style.paddingLeft = "20px";
      for (var i = 0; i < node.children.length; ++i)
        drawTree(sub, node.children[i]);
    }
  }
  drawTree(out, editor.getDoc());
}

function fillEditor() {
  var sc = document.getElementById("me");
  var doc = (sc.textContent || sc.innerText || sc.innerHTML).replace(/^\s*/, "") + "\n";
  doc += doc; doc += doc; doc += doc; doc += doc; doc += doc; doc += doc;
  editor.setValue(doc);
}
    </script>

<p><button onclick="fillEditor()">Add a lot of content</button></p>

  </article>
