<!doctype html>

<title>CodeMirror: Bi-directional Text Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/xml/xml.js"></script>
<style type="text/css">
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Bi-directional Text</a>
  </ul>
</div>

<article>
<h2>Bi-directional Text Demo</h2>
<form><textarea id="code" name="code"><!-- Piece of the CodeMirror manual, 'translated' into Arabic by
     Google Translate -->

<dl>
  <dt id=option_value><code>value (string or Doc)</code></dt>
  <dd>قيمة البداية المحرر. يمكن أن تكون سلسلة، أو. كائن مستند.</dd>
  <dt id=option_mode><code>mode (string or object)</code></dt>
  <dd>وضع الاستخدام. عندما لا تعطى، وهذا الافتراضي إلى الطريقة الاولى
  التي تم تحميلها. قد يكون من سلسلة، والتي إما أسماء أو ببساطة هو وضع
  MIME نوع المرتبطة اسطة. بدلا من ذلك، قد يكون من كائن يحتوي على
  خيارات التكوين لواسطة، مع <code>name</code> الخاصية التي وضع أسماء
  (على سبيل المثال <code>{name: "javascript", json: true}</code>).
  صفحات التجريبي لكل وضع تحتوي على معلومات حول ما معلمات تكوين وضع
  يدعمها. يمكنك أن تطلب CodeMirror التي تم تعريفها طرق وأنواع MIME
  الكشف على <code>CodeMirror.modes</code>
  و <code>CodeMirror.mimeModes</code> الكائنات. وضع خرائط الأسماء
  الأولى لمنشئات الخاصة بهم، وخرائط لأنواع MIME 2 المواصفات
  واسطة.</dd>
  <dt id=option_theme><code>theme (string)</code></dt>
  <dd>موضوع لنمط المحرر مع. يجب عليك التأكد من الملف CSS تحديد
  المقابلة <code>.cm-s-[name]</code> يتم تحميل أنماط (انظر
  <a href=../theme/><code>theme</code></a> الدليل في التوزيع).
  الافتراضي هو <code>"default"</code> ، والتي تم تضمينها في
  الألوان <code>codemirror.css</code>. فمن الممكن استخدام فئات متعددة
  في تطبيق السمات مرة واحدة على سبيل المثال <code>"foo bar"</code>
  سيتم تعيين كل من <code>cm-s-foo</code> و <code>cm-s-bar</code>
  الطبقات إلى المحرر.</dd>
</dl>
</textarea></form>

    <script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  mode: "text/html",
  lineNumbers: true
});
</script>

  <p>Demonstration of bi-directional text support. See
  the <a href="http://marijnhaverbeke.nl/blog/cursor-in-bidi-text.html">related
  blog post</a> for more background.</p>

  <p><strong>Note:</strong> There is
  a <a href="https://github.com/marijnh/CodeMirror/issues/1757">known
  bug</a> with cursor motion and mouse clicks in bi-directional lines
  that are line wrapped.</p>

</article>
