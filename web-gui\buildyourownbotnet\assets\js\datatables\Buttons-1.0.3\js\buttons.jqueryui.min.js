(function(b,a){b.extend(!0,a.Buttons.defaults,{dom:{container:{className:"dt-buttons ui-buttonset"},button:{className:"dt-button ui-button ui-state-default ui-button-text-only",disabled:"ui-state-disabled",active:"ui-state-active"},buttonLiner:{tag:"span",className:"ui-button-text"}}});a.ext.buttons.collection.text=function(a){return a.i18n("buttons.collection",'Collection <span class="ui-button-icon-primary ui-icon ui-icon-triangle-1-s"/>')}})(jQuery,jQuery.fn.dataTable);
