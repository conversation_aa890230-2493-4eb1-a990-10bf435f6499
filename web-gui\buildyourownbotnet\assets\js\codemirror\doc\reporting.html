<!doctype html>

<title>CodeMirror: Reporting Bugs</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">

<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Reporting bugs</a>
  </ul>
</div>

<article>

<h2>Reporting bugs effectively</h2>

<div class="left">

<p>So you found a problem in CodeMirror. By all means, report it! Bug
reports from users are the main drive behind improvements to
CodeMirror. But first, please read over these points:</p>

<ol>
  <li>CodeMirror is maintained by volunteers. They don't owe you
  anything, so be polite. Reports with an indignant or belligerent
  tone tend to be moved to the bottom of the pile.</li>

  <li>Include information about <strong>the browser in which the
  problem occurred</strong>. Even if you tested several browsers, and
  the problem occurred in all of them, mention this fact in the bug
  report. Also include browser version numbers and the operating
  system that you're on.</li>

  <li>Mention which release of CodeMirror you're using. Preferably,
  try also with the current development snapshot, to ensure the
  problem has not already been fixed.</li>

  <li>Mention very precisely what went wrong. "X is broken" is not a
  good bug report. What did you expect to happen? What happened
  instead? Describe the exact steps a maintainer has to take to make
  the problem occur. We can not fix something that we can not
  observe.</li>

  <li>If the problem can not be reproduced in any of the demos
  included in the CodeMirror distribution, please provide an HTML
  document that demonstrates the problem. The best way to do this is
  to go to <a href="http://jsbin.com/ihunin/edit">jsbin.com</a>, enter
  it there, press save, and include the resulting link in your bug
  report.</li>
</ol>

</div>

</article>
