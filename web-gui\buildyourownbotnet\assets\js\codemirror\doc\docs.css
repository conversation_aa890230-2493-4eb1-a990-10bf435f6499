@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro'), local('SourceSansPro-Regular'), url(http://themes.googleusercontent.com/static/fonts/sourcesanspro/v5/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff');
}

body, html { margin: 0; padding: 0; height: 100%; }
section, article { display: block; padding: 0; }

body {
  background: #f8f8f8;
  font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif;
  line-height: 1.5;
}

p { margin-top: 0; }

h2, h3 {
  font-weight: normal;
  margin-bottom: .7em;
}
h2 { font-size: 120%; }
h3 { font-size: 110%; }
article > h2:first-child, section:first-child > h2 { margin-top: 0; }

a, a:visited, a:link, .quasilink {
  color: #A21313;
  text-decoration: none;
}

em {
  padding-right: 2px;
}

.quasilink {
  cursor: pointer;
}

article {
  max-width: 700px;
  margin: 0 0 0 160px;
  border-left: 2px solid #E30808;
  border-right: 1px solid #ddd;
  padding: 30px 50px 100px 50px;
  background: white;
  z-index: 2;
  position: relative;
  min-height: 100%;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
}

#nav {
  position: fixed;
  top: 30px;
  left: 0; right: none;
  width: 160px;
  padding-right: 350px;
  text-align: right;
  z-index: 1;
}

@media screen and (min-width: 1000px) {
  article {
    margin: 0 auto;
  }
  #nav {
    right: 50%;
    width: auto;
  }
}

#nav ul {
  display: block;
  margin: 0; padding: 0;
  margin-bottom: 32px;
}

#nav li {
  display: block;
  margin-bottom: 4px;
}

#nav li ul {
  font-size: 80%;
  margin-bottom: 0;
  display: none;
}

#nav li.active ul {
  display: block;
}

#nav li li a {
  padding-right: 20px;
}

#nav ul a {
  color: black;
  padding: 0 7px 1px 11px;
}

#nav ul a.active, #nav ul a:hover {
  border-bottom: 1px solid #E30808;
  color: #E30808;
}

#logo {
  border: 0;
  margin-right: 7px;
  margin-bottom: 25px;
}

section {
  border-top: 1px solid #E30808;
  margin: 1.5em 0;
}

section.first {
  border: none;
  margin-top: 0;
}

#demo {
  position: relative;
}

#demolist {
  position: absolute;
  right: 5px;
  top: 5px;
  z-index: 25;
}

#bankinfo {
  text-align: left;
  display: none;
  padding: 0 .5em;
  position: absolute;
  border: 2px solid #aaa;
  border-radius: 5px;
  background: #eee;
  top: 10px;
  left: 30px;
}

#bankinfo_close {
  position: absolute;
  top: 0; right: 6px;
  font-weight: bold;
  cursor: pointer;
}

.bigbutton {
  cursor: pointer;
  text-align: center;
  padding: 0 1em;
  display: inline-block;
  color: white;
  position: relative;
  line-height: 1.9;
  color: white !important;
  background: #A21313;
}

.bigbutton.right {
  border-bottom-left-radius: 100px;
  border-top-left-radius: 100px;
}

.bigbutton.left {
  border-bottom-right-radius: 100px;
  border-top-right-radius: 100px;
}

.bigbutton:hover {
  background: #E30808;
}

th {
  text-decoration: underline;
  font-weight: normal;
  text-align: left;
}

#features ul {
  list-style: none;
  margin: 0 0 1em;
  padding: 0 0 0 1.2em;
}

#features li:before {
  content: "-";
  width: 1em;
  display: inline-block;
  padding: 0;
  margin: 0;
  margin-left: -1em;
}

.rel {
  margin-bottom: 0;
}
.rel-note {
  margin-top: 0;
  color: #555;
}

pre {
  padding-left: 15px;
  border-left: 2px solid #ddd;
}

code {
  padding: 0 2px;
}

strong {
  text-decoration: underline;
  font-weight: normal;
}

.field {
  border: 1px solid #A21313;
}
