<!doctype html>

<title>CodeMirror: <PERSON><PERSON></title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<link rel="stylesheet" href="../addon/lint/lint.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/javascript/javascript.js"></script>
<script src="../mode/css/css.js"></script>
<script src="http://ajax.aspnetcdn.com/ajax/jshint/r07/jshint.js"></script>
<script src="https://rawgithub.com/zaach/jsonlint/79b553fb65c192add9066da64043458981b3972b/lib/jsonlint.js"></script>
<script src="https://rawgithub.com/stubbornella/csslint/master/release/csslint.js"></script>
<script src="../addon/lint/lint.js"></script>
<script src="../addon/lint/javascript-lint.js"></script>
<script src="../addon/lint/json-lint.js"></script>
<script src="../addon/lint/css-lint.js"></script>
<style type="text/css">
      .CodeMirror {border: 1px solid black;}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Linter</a>
  </ul>
</div>

<article>
<h2>Linter Demo</h2>


    <p><textarea id="code-js">var widgets = []
function updateHints() {
  editor.operation(function(){
    for (var i = 0; i < widgets.length; ++i)
      editor.removeLineWidget(widgets[i]);
    widgets.length = 0;

    JSHINT(editor.getValue());
    for (var i = 0; i < JSHINT.errors.length; ++i) {
      var err = JSHINT.errors[i];
      if (!err) continue;
      var msg = document.createElement("div");
      var icon = msg.appendChild(document.createElement("span"));
      icon.innerHTML = "!!";
      icon.className = "lint-error-icon";
      msg.appendChild(document.createTextNode(err.reason));
      msg.className = "lint-error";
      widgets.push(editor.addLineWidget(err.line - 1, msg, {coverGutter: false, noHScroll: true}));
    }
  });
  var info = editor.getScrollInfo();
  var after = editor.charCoords({line: editor.getCursor().line + 1, ch: 0}, "local").top;
  if (info.top + info.clientHeight < after)
    editor.scrollTo(null, after - info.clientHeight + 3);
}
</textarea></p>

    <p><textarea id="code-json">[
 {
  _id: "post 1",
  "author": "Bob",
  "content": "...",
  "page_views": 5
 },
 {
  "_id": "post 2",
  "author": "Bob",
  "content": "...",
  "page_views": 9
 },
 {
  "_id": "post 3",
  "author": "Bob",
  "content": "...",
  "page_views": 8
 }
]
</textarea></p>

    <p><textarea id="code-css">@charset "UTF-8";

@import url("booya.css") print, screen;
@import "whatup.css" screen;
@import "wicked.css";

/*Error*/
@charset "UTF-8";


@namespace "http://www.w3.org/1999/xhtml";
@namespace svg "http://www.w3.org/2000/svg";

/*Warning: empty ruleset */
.foo {
}

h1 {
    font-weight: bold;
}

/*Warning: qualified heading */
.foo h1 {
    font-weight: bold;
}

/*Warning: adjoining classes */
.foo.bar {
    zoom: 1;
}

li.inline {
    width: 100%;  /*Warning: 100% can be problematic*/
}

li.last {
  display: inline;
  padding-left: 3px !important;
  padding-right: 3px;
  border-right: 0px;
}

@media print {
    li.inline {
      color: black;
    }
}

@page {
  margin: 10%;
  counter-increment: page;

  @top-center {
    font-family: sans-serif;
    font-weight: bold;
    font-size: 2em;
    content: counter(page);
  }
}
</textarea></p>
<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code-js"), {
    lineNumbers: true,
    mode: "javascript",
    gutters: ["CodeMirror-lint-markers"],
    lint: true
  });

  var editor_json = CodeMirror.fromTextArea(document.getElementById("code-json"), {
    lineNumbers: true,
    mode: "application/json",
    gutters: ["CodeMirror-lint-markers"],
    lint: true
  });
  
  var editor_css = CodeMirror.fromTextArea(document.getElementById("code-css"), {
    lineNumbers: true,
    mode: "css",
    gutters: ["CodeMirror-lint-markers"],
    lint: true
  });
</script>

  </article>
