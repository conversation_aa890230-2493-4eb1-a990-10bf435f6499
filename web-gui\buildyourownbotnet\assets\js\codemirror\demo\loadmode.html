<!doctype html>

<title>CodeMirror: Lazy Mode Loading Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/mode/loadmode.js"></script>
<style type="text/css">
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Lazy Mode Loading</a>
  </ul>
</div>

<article>
<h2>Lazy Mode Loading Demo</h2>
<form><textarea id="code" name="code">This is the editor.
// It starts out in plain text mode,
#  use the control below to load and apply a mode
  "you'll see the highlighting of" this text /*change*/.
</textarea></form>
<p><input type=text value=javascript id=mode> <button type=button onclick="change()">change mode</button></p>

    <script>
CodeMirror.modeURL = "../mode/%N/%N.js";
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  lineNumbers: true
});
var modeInput = document.getElementById("mode");
CodeMirror.on(modeInput, "keypress", function(e) {
  if (e.keyCode == 13) change();
});
function change() {
   editor.setOption("mode", modeInput.value);
   CodeMirror.autoLoadMode(editor, modeInput.value);
}
</script>
  </article>
