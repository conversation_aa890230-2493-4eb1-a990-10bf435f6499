[{"id": 1, "label": "Folder A", "inode": true, "open": false, "branch": [{"id": 2, "label": "Folder A.1", "inode": false, "my-hash": "hash-2", "my-url": "sample.php?request=page-2"}, {"id": 3, "label": "Folder A.2", "inode": false, "my-hash": "hash-3", "my-url": "sample.php?request=page-3"}, {"id": 4, "label": "file a.1", "inode": false, "my-hash": "hash-4", "my-url": "sample.php?request=page-4"}, {"id": 5, "label": "file a.2", "inode": false, "my-hash": "hash-5", "my-url": "sample.php?request=page-5"}], "my-hash": "hash-1", "my-url": "sample.php?request=page-1"}, {"id": 6, "label": "Folder B", "inode": false, "my-hash": "hash-6", "my-url": "sample.php?request=page-6"}, {"id": 7, "label": "Folder C", "inode": true, "open": false, "branch": [{"id": 8, "label": "Folder C.1", "inode": true, "open": false, "branch": [{"id": 9, "label": "Folder C.1.1", "inode": false, "my-hash": "hash-9", "my-url": "sample.php?request=page-9"}, {"id": 10, "label": "Folder C.1.2", "inode": false, "my-hash": "hash-10", "my-url": "sample.php?request=page-10"}, {"id": 11, "label": "file c.1.1", "inode": false, "my-hash": "hash-11", "my-url": "sample.php?request=page-11"}], "my-hash": "hash-8", "my-url": "sample.php?request=page-8"}, {"id": 12, "label": "file c.1", "inode": false, "my-hash": "hash-12", "my-url": "sample.php?request=page-12"}], "my-hash": "hash-7", "my-url": "sample.php?request=page-7"}, {"id": 13, "label": "file a", "inode": false, "my-hash": "hash-13", "my-url": "sample.php?request=page-13"}, {"id": 14, "label": "file b", "inode": false, "my-hash": "hash-14", "my-url": "sample.php?request=page-14"}, {"id": 15, "label": "file c", "inode": false, "my-hash": "hash-15", "my-url": "sample.php?request=page-15"}]