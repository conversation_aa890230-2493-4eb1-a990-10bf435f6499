body a {
  color: #981b1b;
}
body .profile-info.dropdown .dropdown-menu {
  background: #981b1b;
  border-color: #981b1b;
}
body .profile-info.dropdown .dropdown-menu > li {
  border-bottom-color: transparent;
}
body .profile-info.dropdown .dropdown-menu li a {
  color: #f8e0e0;
}
body .profile-info.dropdown .dropdown-menu li a:hover {
  background: #891818;
}
body .page-container .sidebar-menu {
  background: #981b1b;
  color: #921a1a;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li#search .search-input {
  background-color: #891818 !important;
  border-color: #a22424 !important;
}
body .page-container .sidebar-menu #main-menu li#search {
  background-color: #891818;
  border-color: #a22424;
}
body .page-container .sidebar-menu #main-menu li ul {
  border-color: rgba(162, 36, 36, 0.5);
}
body .page-container .sidebar-menu #main-menu li ul > li {
  border-color: rgba(162, 36, 36, 0.5);
}
body .page-container .sidebar-menu #main-menu li ul > li > a {
  background-color: #981b1b;
}
body .page-container .sidebar-menu #main-menu li.active > a {
  background: #891818;
}
body .page-container .sidebar-menu #main-menu li ul > li > a {
  background-color: #891818;
}
body .page-container .sidebar-menu .logo-env > div.sidebar-collapse a,
body .page-container .sidebar-menu .logo-env > div.sidebar-mobile-menu a {
  border-color: #a22424;
}
body .page-container .sidebar-menu .logo-env > div.sidebar-collapse a:hover {
  background: #891818;
}
body .page-container .sidebar-menu .sidebar-user-info {
  border-color: #a22424;
}
body .page-container .sidebar-menu .sidebar-user-info .sui-hover {
  background-color: #981b1b;
}
body .page-container .sidebar-menu #main-menu li {
  border-color: #a22424;
}
body .page-container .sidebar-menu #main-menu li a {
  color: #f8e0e0;
}
body .page-container .sidebar-menu #main-menu li a:hover {
  background-color: #921a1a;
}
body .page-container .sidebar-menu #main-menu li ul > li > a:hover {
  background-color: #8f1818;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul li {
  border-color: #a22424;
}
body .page-container .sidebar-menu #main-menu li ul > li ul > li > a {
  background-color: #7a1616;
}
body .page-container .sidebar-menu #main-menu li ul > li ul > li ul > li > a {
  background-color: #7a1616;
}
body .page-container .sidebar-menu #main-menu li ul > li ul > li ul > li ul > li > a {
  background-color: #7a1616;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li > a > span:not(.badge) {
  background: #981b1b;
  border-color: #a22424;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li ul {
  border-color: #a22424;
}
body .profile-info.dropdown .dropdown-menu > .caret {
  border-bottom-color: #981b1b;
}
body #chat {
  background: #981b1b;
}
body #chat .chat-header {
  color: #FFF;
  border-bottom: 1px solid #a22424;
}
body #chat .chat-group > a:hover,
body #chat .chat-group > a.active {
  background: #a22424;
}
body #chat .chat-group > strong {
  color: rgba(255, 255, 255, 0.4);
}
body #chat .chat-conversation {
  background: #7a1616;
}
body #chat .chat-conversation .conversation-body > li.odd,
body #chat .chat-conversation .conversation-body > li.even,
body #chat .chat-conversation .conversation-body > li.opponent {
  background: #891818;
}
body #chat .chat-conversation .conversation-header {
  border-color: #a22424;
}
body #chat .chat-conversation .chat-textarea textarea {
  background: #891818;
  box-shadow: none;
  border-color: #891818;
}
body #chat .chat-group > a:before {
  border-color: transparent transparent transparent #7a1616;
}
body.login-page .login-form .form-group .input-group {
  border-color: #a22424;
}
body.login-page {
  background: #891818;
  color: rgba(255, 255, 255, 0.5);
}
body.login-page .login-form .form-group .input-group .form-control::-webkit-input-placeholder {
  color: #f8e0e0;
}
body.login-page .login-form .form-group .input-group .form-control:-moz-placeholder {
  color: #f8e0e0;
}
body.login-page .login-form .form-group .input-group .form-control::-moz-placeholder {
  color: #f8e0e0;
}
body.login-page .login-form .form-group .input-group .form-control:-ms-input-placeholder {
  color: #f8e0e0;
}
body.login-page .login-form .form-group .input-group {
  background: #981b1b;
  border-color: #a22424;
}
body.login-page .login-form .form-group .input-group.focused {
  border-color: #b72929;
}
body.login-page .login-form .form-group .input-group .input-group-addon:after {
  background: #a22424;
}
body.login-page .login-form .form-group .btn-login {
  background: #891818;
  border-color: #a22424;
}
body.login-page .login-form .form-group .btn-login:hover {
  background: #981b1b;
}
body .login-container .login-header {
  background-color: #981b1b;
}
body .login-container .login-header.login-caret:after {
  border-top-color: #981b1b;
}
body.login-page.logging-in .login-progressbar {
  background: #cc2424;
  height: 2px;
}
body.login-page.logging-in .login-progressbar div {
  background: #ffba00;
}
body .tile-primary {
  background: #981b1b;
}
body .tile-primary .tile-entry {
  border-color: #a22424;
}
body .tile-primary .title {
  background: #821717;
}
body .tile-white-primary .num,
body .tile-white-primary h3,
body .tile-white-primary p {
  color: #a22424;
}
body .btn-primary {
  background: #981b1b;
  border-color: #981b1b;
}
body .panel-invert {
  background: #981b1b;
}
body .navbar-inverse {
  border-color: #981b1b;
  background: #981b1b;
}
body .navbar-inverse .navbar-nav > li > a {
  color: #f8e0e0;
}
body .navbar-inverse .navbar-nav > .open > a,
body .navbar-inverse .navbar-nav > .open > a:hover,
body .navbar-inverse .navbar-nav > .open > a:focus {
  background: #891818;
}
body .navbar-inverse .navbar-nav > .active > a,
body .navbar-inverse .navbar-nav > .active > a:hover,
body .navbar-inverse .navbar-nav > .active > a:focus {
  background: #891818;
}
body .badge.badge-primary,
body .label-primary {
  background-color: #981b1b;
}
body .badge.badge-secondary,
body .label-secondary {
  background-color: #ffba00;
}
body .pagination > .active > a,
body .pagination > .active > span,
body .pagination > .active > a:hover,
body .pagination > .active > span:hover,
body .pagination > .active > a:focus,
body .pagination > .active > span:focus {
  border-color: #981b1b;
  background: #981b1b;
}
body div.datepicker table tr td.active,
body div.datepicker table tr td.active:hover,
body div.datepicker table tr td.active.disabled,
body div.datepicker table tr td.active.disabled:hover {
  background-color: #981b1b;
}
body.login-page .login-form .form-group.lockscreen-input .lockscreen-thumb img {
  border-color: #891818;
}
body.login-page .login-content a {
  color: #f8e0e0;
}
body .input-group-addon {
  color: #f8e0e0;
}
body.page-left-in,
body.page-right-in,
body.page-fade-only,
body.page-fade {
  background: #981b1b !important;
}
body .page-container .sidebar-menu #main-menu li#search button i {
  color: #f8e0e0;
}
body .btn-primary.btn-icon i {
  background-color: rgba(0, 0, 0, 0.2);
}
body .btn-primary:hover,
body .btn-primary:focus,
body .btn-primary:active,
body .btn-primary.active,
body .open .dropdown-toggle.btn-primary {
  background: #7a1616;
  border-color: #7a1616;
}
body .tile-block .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #981b1b;
}
body .page-container.horizontal-menu header.navbar {
  background: #981b1b;
}
body .page-container.horizontal-menu.with-sidebar header.navbar {
  border-color: #a22424;
}
body .page-container.horizontal-menu.with-sidebar .sidebar-user-info {
  border-color: #a22424;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li > a {
  border-right-color: rgba(162, 36, 36, 0.5);
  color: #f8e0e0;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li.active > a {
  background: #891818;
}
body .page-container.horizontal-menu header.navbar .navbar-nav {
  border-left-color: rgba(162, 36, 36, 0.5);
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li#search {
  border-right-color: rgba(162, 36, 36, 0.5);
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li:hover > a {
  background: #891818;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul {
  background: #981b1b;
}
body .page-container.horizontal-menu header.navbar ul.nav > li.dropdown.open {
  background: #891818;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li a {
  border-color: #a22424;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li:hover > a {
  background: #891818;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li.active > a {
  background: #891818;
}
body .page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input,
body .page-container.horizontal-menu header.navbar > ul > li#search .search-input {
  background: #891818;
  border-color: #a22424;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li#search.search-input-collapsed:hover {
  border-color: #a22424;
  background: #891818;
}
body .page-container.horizontal-menu header.navbar ul.nav > li.sep {
  border-color: #a22424;
}
body .page-container.horizontal-menu header.navbar ul.nav > li > a,
body .page-container.horizontal-menu header.navbar ul.nav > li > span {
  color: #f8e0e0;
}
body .entypo-menu {
  color: #f8e0e0;
}
body .page-container .sidebar-menu #main-menu li#search .search-input {
  color: #f8e0e0;
}
body .page-container .sidebar-menu #main-menu li#search .search-input::-webkit-input-placeholder {
  color: #f8e0e0;
}
body .page-container .sidebar-menu #main-menu li#search .search-input:-moz-placeholder {
  color: #f8e0e0;
}
body .page-container .sidebar-menu #main-menu li#search .search-input::-moz-placeholder {
  color: #f8e0e0;
}
body .page-container .sidebar-menu #main-menu li#search .search-input:-ms-input-placeholder {
  color: #f8e0e0;
}
body #chat .chat-group > a {
  color: #f8e0e0;
}
body .conversation-body,
body #chat .entypo-cancel,
body #chat .chat-conversation .chat-textarea:after {
  color: #f8e0e0;
}
body #chat .chat-conversation .chat-textarea textarea::-webkit-input-placeholder {
  color: #f8e0e0;
}
body #chat .chat-conversation .chat-textarea textarea:-moz-placeholder {
  color: #f8e0e0;
}
body #chat .chat-conversation .chat-textarea textarea::-moz-placeholder {
  color: #f8e0e0;
}
body #chat .chat-conversation .chat-textarea textarea:-ms-input-placeholder {
  color: #f8e0e0;
}
body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li a {
  color: #f8e0e0;
}
body .page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search button i,
body .page-container.horizontal-menu header.navbar > ul > li#search button i {
  color: #f8e0e0;
}
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover.has-sub > a:hover,
body .page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover.has-sub > a:hover {
  color: #f8e0e0;
}
body .panel-invert > .panel-heading,
body .modal.invert .modal-dialog .modal-content .modal-header,
body .modal.invert .modal-dialog .modal-content .modal-footer {
  background: #981b1b;
  border-color: #a22424;
}
body .panel-invert > .panel-body,
body .modal.invert .modal-dialog .modal-content {
  background: #981b1b;
  color: #f8e0e0;
}
body .modal.invert .modal-dialog .modal-content {
  border-color: #981b1b;
}
body .panel-invert {
  border-color: #981b1b;
}
body .panel-invert > .panel-heading > .panel-options > a.bg,
body .modal.invert .modal-dialog .modal-content .modal-header .close {
  background-color: #891818;
}
body .panel-invert > .panel-heading > .panel-options > a.bg:hover {
  background-color: #7a1616;
}
body a.list-group-item.active,
body a.list-group-item.active:hover,
body a.list-group-item.active:focus {
  background-color: #981b1b;
  border-color: #981b1b;
}
body a.list-group-item.active .list-group-item-text,
body a.list-group-item.active:hover .list-group-item-text,
body a.list-group-item.active:focus .list-group-item-text {
  color: #f8e0e0;
}
body .popover.popover-primary {
  background-color: #981b1b;
  border-color: #981b1b;
}
body .popover.popover-primary .popover-title {
  background-color: #7a1616;
  border-color: #7a1616;
}
body .popover.popover-primary.top .arrow {
  border-top-color: #981b1b;
}
body .popover.popover-primary.top .arrow:after {
  border-top-color: #981b1b;
}
body .popover.popover-primary.right .arrow {
  border-right-color: #981b1b;
}
body .popover.popover-primary.right .arrow:after {
  border-right-color: #981b1b;
}
body .popover.popover-primary.bottom .arrow {
  border-bottom-color: #981b1b;
}
body .popover.popover-primary.bottom .arrow:after {
  border-bottom-color: #981b1b;
}
body .popover.popover-primary.left .arrow {
  border-left-color: #981b1b;
}
body .popover.popover-primary.left .arrow:after {
  border-left-color: #981b1b;
}
body .popover.popover-secondary {
  background-color: #ffba00;
  border-color: #ffba00;
}
body .popover.popover-secondary .popover-title {
  background-color: #ffba00;
  border-color: #ffba00;
}
body .popover.popover-secondary.top .arrow {
  border-top-color: #ffba00;
}
body .popover.popover-secondary.top .arrow:after {
  border-top-color: #ffba00;
}
body .popover.popover-secondary.right .arrow {
  border-right-color: #ffba00;
}
body .popover.popover-secondary.right .arrow:after {
  border-right-color: #ffba00;
}
body .popover.popover-secondary.bottom .arrow {
  border-bottom-color: #ffba00;
}
body .popover.popover-secondary.bottom .arrow:after {
  border-bottom-color: #ffba00;
}
body .popover.popover-secondary.left .arrow {
  border-left-color: #ffba00;
}
body .popover.popover-secondary.left .arrow:after {
  border-left-color: #ffba00;
}
body .tooltip.tooltip-primary .tooltip-inner {
  background-color: #981b1b;
  color: #f8e0e0;
}
body .tooltip.tooltip-primary.top .tooltip-arrow {
  border-top-color: #981b1b;
}
body .tooltip.tooltip-primary.top-left .tooltip-arrow {
  border-top-color: #981b1b;
}
body .tooltip.tooltip-primary.top-right .tooltip-arrow {
  border-top-color: #981b1b;
}
body .tooltip.tooltip-primary.right .tooltip-arrow {
  border-right-color: #981b1b;
}
body .tooltip.tooltip-primary.left .tooltip-arrow {
  border-left-color: #981b1b;
}
body .tooltip.tooltip-primary.bottom .tooltip-arrow {
  border-bottom-color: #981b1b;
}
body .tooltip.tooltip-primary.bottom-left .tooltip-arrow {
  border-bottom-color: #981b1b;
}
body .tooltip.tooltip-primary.bottom-right .tooltip-arrow {
  border-bottom-color: #981b1b;
}
body .tooltip.tooltip-secondary .tooltip-inner {
  background-color: #ffba00;
  color: #f8e0e0;
}
body .tooltip.tooltip-secondary.top .tooltip-arrow {
  border-top-color: #ffba00;
}
body .tooltip.tooltip-secondary.top-left .tooltip-arrow {
  border-top-color: #ffba00;
}
body .tooltip.tooltip-secondary.top-right .tooltip-arrow {
  border-top-color: #ffba00;
}
body .tooltip.tooltip-secondary.right .tooltip-arrow {
  border-right-color: #ffba00;
}
body .tooltip.tooltip-secondary.left .tooltip-arrow {
  border-left-color: #ffba00;
}
body .tooltip.tooltip-secondary.bottom .tooltip-arrow {
  border-bottom-color: #ffba00;
}
body .tooltip.tooltip-secondary.bottom-left .tooltip-arrow {
  border-bottom-color: #ffba00;
}
body .tooltip.tooltip-secondary.bottom-right .tooltip-arrow {
  border-bottom-color: #ffba00;
}
body .horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input::-webkit-input-placeholder,
body .horizontal-menu header.navbar > ul > li#search .search-input::-webkit-input-placeholder {
  color: #f8e0e0;
}
body .horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input:-moz-placeholder,
body .horizontal-menu header.navbar > ul > li#search .search-input:-moz-placeholder {
  color: #f8e0e0;
}
body .horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input::-moz-placeholder,
body .horizontal-menu header.navbar > ul > li#search .search-input::-moz-placeholder {
  color: #f8e0e0;
}
body .horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input:-ms-input-placeholder,
body .horizontal-menu header.navbar > ul > li#search .search-input:-ms-input-placeholder {
  color: #f8e0e0;
}
