.form-control:focus {
  border-color: #cbd0d9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(203, 208, 217, 0.6);
  -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(203, 208, 217, 0.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(203, 208, 217, 0.6);
}
.form-control::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: #999999;
}
.form-control::-webkit-input-placeholder {
  color: #999999;
}
.form-control::-webkit-input-placeholder {
  color: #aaa;
}
.form-control:-moz-placeholder {
  color: #aaa;
}
.form-control::-moz-placeholder {
  color: #aaa;
}
.form-control:-ms-input-placeholder {
  color: #aaa;
}
select.input-sm {
  height: 28px;
  line-height: 28px;
}
textarea.input-sm,
select[multiple].input-sm {
  height: auto;
}
select.input-lg {
  height: 42px;
  line-height: 42px;
}
textarea.input-lg,
select[multiple].input-lg {
  height: auto;
}
.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label {
  color: #045702;
}
.has-success .form-control {
  border-color: #045702;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-success .form-control:focus {
  border-color: #022501;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #09bb04;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #09bb04;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #09bb04;
}
.has-success .input-group-addon {
  color: #045702;
  border-color: #045702;
  background-color: #bdedbc;
}
.has-success .form-control-feedback {
  color: #045702;
}
.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label {
  color: #574802;
}
.has-warning .form-control {
  border-color: #574802;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-warning .form-control:focus {
  border-color: #251f01;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #bb9b04;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #bb9b04;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #bb9b04;
}
.has-warning .input-group-addon {
  color: #574802;
  border-color: #574802;
  background-color: #ffefa4;
}
.has-warning .form-control-feedback {
  color: #574802;
}
.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline,
.has-error.radio label,
.has-error.checkbox label,
.has-error.radio-inline label,
.has-error.checkbox-inline label {
  color: #ac1818;
}
.has-error .form-control {
  border-color: #ac1818;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-error .form-control:focus {
  border-color: #7f1212;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #e54545;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #e54545;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #e54545;
}
.has-error .input-group-addon {
  color: #ac1818;
  border-color: #ac1818;
  background-color: #ffc9c9;
}
.has-error .form-control-feedback {
  color: #ac1818;
}
.morris-hover {
  position: absolute;
  z-index: 1000;
}
.morris-hover.morris-default-style {
  border-radius: 10px;
  padding: 6px;
  color: #666;
  background: rgba(255, 255, 255, 0.8);
  border: solid 2px rgba(230, 230, 230, 0.8);
  font-family: sans-serif;
  font-size: 12px;
  text-align: center;
}
.morris-hover.morris-default-style .morris-hover-row-label {
  font-weight: bold;
  margin: 0.25em 0;
}
.morris-hover.morris-default-style .morris-hover-point {
  white-space: nowrap;
  margin: 0.1em 0;
}
.scrollbar-rail-default {
  display: none;
  position: absolute;
  /* please don't change 'position' */
  -webkit-border-radius: 0px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0px;
  -moz-background-clip: padding;
  border-radius: 0px;
  background-clip: padding-box;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
  -moz-transition: background-color 0.2s linear, opacity 0.2s linear;
  -o-transition: background-color 0.2s linear, opacity 0.2s linear;
  transition: background-color 0.2s linear, opacity 0.2s linear;
}
.scrollbar-rail-hover {
  background-color: #eee;
  zoom: 1;
  filter: alpha(opacity=90);
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
}
.scrollbar-default {
  position: absolute;
  /* please don't change 'position' */
  background-color: #aaa;
  -webkit-border-radius: 0px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0px;
  -moz-background-clip: padding;
  border-radius: 0px;
  background-clip: padding-box;
  -webkit-transition: background-color 0.2s linear;
  -moz-transition: background-color 0.2s linear;
  -o-transition: background-color 0.2s linear;
  transition: background-color 0.2s linear;
}
.scrollbar-hover {
  background-color: #999;
}
.in-scrolling.in-scrolling {
  background-color: #eee;
  zoom: 1;
  filter: alpha(opacity=90);
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
}
.ps-container.ps-active-x > .ps-scrollbar-x-rail,
.ps-container.ps-active-y > .ps-scrollbar-y-rail {
  display: block;
}
.ps-container > .ps-scrollbar-x-rail {
  display: none;
  position: absolute;
  /* please don't change 'position' */
  -webkit-border-radius: 0px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0px;
  -moz-background-clip: padding;
  border-radius: 0px;
  background-clip: padding-box;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
  -moz-transition: background-color 0.2s linear, opacity 0.2s linear;
  -o-transition: background-color 0.2s linear, opacity 0.2s linear;
  transition: background-color 0.2s linear, opacity 0.2s linear;
  bottom: 3px;
  /* there must be 'bottom' for ps-scrollbar-x-rail */
  height: 8px;
  z-index: 1000;
}
.ps-container > .ps-scrollbar-x-rail > .ps-scrollbar-x {
  position: absolute;
  /* please don't change 'position' */
  background-color: #aaa;
  -webkit-border-radius: 0px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0px;
  -moz-background-clip: padding;
  border-radius: 0px;
  background-clip: padding-box;
  -webkit-transition: background-color 0.2s linear;
  -moz-transition: background-color 0.2s linear;
  -o-transition: background-color 0.2s linear;
  transition: background-color 0.2s linear;
  bottom: 0;
  /* there must be 'bottom' for ps-scrollbar-x */
  height: 8px;
}
.ps-container > .ps-scrollbar-x-rail.in-scrolling {
  background-color: #eee;
  zoom: 1;
  filter: alpha(opacity=90);
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
}
.ps-container > .ps-scrollbar-y-rail {
  display: none;
  position: absolute;
  /* please don't change 'position' */
  -webkit-border-radius: 0px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0px;
  -moz-background-clip: padding;
  border-radius: 0px;
  background-clip: padding-box;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
  -moz-transition: background-color 0.2s linear, opacity 0.2s linear;
  -o-transition: background-color 0.2s linear, opacity 0.2s linear;
  transition: background-color 0.2s linear, opacity 0.2s linear;
  right: 2px;
  /* there must be 'right' for ps-scrollbar-y-rail */
  width: 5px;
  z-index: 1000;
}
.ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y {
  position: absolute;
  /* please don't change 'position' */
  background-color: #aaa;
  -webkit-border-radius: 0px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0px;
  -moz-background-clip: padding;
  border-radius: 0px;
  background-clip: padding-box;
  -webkit-transition: background-color 0.2s linear;
  -moz-transition: background-color 0.2s linear;
  -o-transition: background-color 0.2s linear;
  transition: background-color 0.2s linear;
  right: 0;
  /* there must be 'right' for ps-scrollbar-y */
  width: 5px;
}
.ps-container > .ps-scrollbar-y-rail.in-scrolling {
  background-color: #eee;
  zoom: 1;
  filter: alpha(opacity=90);
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
}
.ps-container:hover > .ps-scrollbar-x-rail,
.ps-container:hover > .ps-scrollbar-y-rail {
  zoom: 1;
  filter: alpha(opacity=60);
  -webkit-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
}
.ps-container:hover > .ps-scrollbar-x-rail.in-scrolling,
.ps-container:hover > .ps-scrollbar-y-rail.in-scrolling {
  background-color: #eee;
  zoom: 1;
  filter: alpha(opacity=90);
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
}
.ps-container:hover > .ps-scrollbar-x-rail:hover {
  background-color: #eee;
  zoom: 1;
  filter: alpha(opacity=90);
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
}
.ps-container:hover > .ps-scrollbar-x-rail:hover > .ps-scrollbar-x {
  background-color: #999;
}
.ps-container:hover > .ps-scrollbar-y-rail:hover {
  background-color: #eee;
  zoom: 1;
  filter: alpha(opacity=90);
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
}
.ps-container:hover > .ps-scrollbar-y-rail:hover > .ps-scrollbar-y {
  background-color: #999;
}
.panel {
  margin-bottom: 17px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 3px;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.panel > .panel-heading .panel-title {
  font-size: 12px;
}
.panel > .panel-heading .panel-title h4 {
  font-size: 14px;
  color: #303641;
}
.panel.panel-shadow {
  -webkit-box-shadow: 0 3px 1px rgba(0,0,0,.04);
  -moz-box-shadow: 0 3px 1px rgba(0,0,0,.04);
  box-shadow: 0 3px 1px rgba(0,0,0,.04);
}
.panel.reloading > .panel-heading > .panel-options > a[rel="reload"] > i {
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
  -webkit-animation: reloadingAnimation 1s linear infinite;
  -moz-animation: reloadingAnimation 1s linear infinite;
  -o-animation: reloadingAnimation 1s linear infinite;
  animation: reloadingAnimation 1s linear infinite;
}
.panel.panel-collapse > .panel-heading > .panel-options > a[rel="collapse"] > i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.panel .blockUI.blockOverlay {
  background-image: url(../images/loader-1.gif);
  background-image: url(../images/loader-2.gif);
  background-repeat: no-repeat;
  background-position: center center;
}
.panel[data-collapsed="1"] .panel-body {
  display: none;
}
.panel[data-collapsed="1"] .panel-heading > .panel-options > a[rel="collapse"] > i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.panel.minimal > .panel-heading {
  padding: 0;
  border-bottom: 1px solid #ebebeb;
}
.panel.minimal > .panel-heading > .panel-title {
  position: relative;
  color: #303641;
  padding: 0;
  padding-bottom: 10px;
  top: 8px;
}
.panel.minimal > .panel-heading > .panel-options {
  padding-right: 0;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li a {
  padding: 8px 10px;
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li a i {
  color: #d0d3d9;
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li a:hover {
  background-color: #f0f0f1;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li.active a {
  border-bottom-color: #fff;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li.active a:hover {
  background-color: #fff;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li.active a i {
  color: #303641;
}
.panel.minimal > .panel-body {
  padding-left: 0;
  padding-right: 0;
}
.panel.minimal.minimal-gray > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #f3f3f3;
  border-color: #ebebeb;
  color: rgba(115, 120, 129, 0.75);
}
.panel.minimal.minimal-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
  color: #303641;
}
.panel-body {
  position: relative;
  padding: 15px;
}
.panel-body:before,
.panel-body:after {
  content: " ";
  display: table;
}
.panel-body:after {
  clear: both;
}
.panel-body.no-padding {
  padding: 0;
}
.panel > .list-group {
  margin-bottom: 0;
}
.panel > .list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0;
}
.panel > .list-group .list-group-item:first-child {
  border-top: 0;
}
.panel > .list-group .list-group-item:last-child {
  border-bottom: 0;
}
.panel > .list-group:first-child .list-group-item:first-child {
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}
.panel > .list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}
.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.panel > .table,
.panel > .table-responsive > .table {
  margin-bottom: 0;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
  border-top-left-radius: 2px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
  border-top-right-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-left-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-right-radius: 2px;
}
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive {
  border-top: 1px solid #ebebeb;
}
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
  border: 0;
}
.panel > .table-bordered > thead > tr > th:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.panel > .table-bordered > tbody > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.panel > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-bordered > thead > tr > td:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.panel > .table-bordered > tbody > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.panel > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
  border-left: 0;
}
.panel > .table-bordered > thead > tr > th:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.panel > .table-bordered > tbody > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.panel > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-bordered > thead > tr > td:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.panel > .table-bordered > tbody > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.panel > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
  border-right: 0;
}
.panel > .table-bordered > thead > tr:first-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
.panel > .table-bordered > tbody > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th,
.panel > .table-bordered > tfoot > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:first-child > th,
.panel > .table-bordered > thead > tr:first-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.panel > .table-bordered > tbody > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.panel > .table-bordered > tfoot > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:first-child > td {
  border-top: 0;
}
.panel > .table-bordered > thead > tr:last-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:last-child > th,
.panel > .table-bordered > tbody > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.panel > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-bordered > thead > tr:last-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:last-child > td,
.panel > .table-bordered > tbody > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.panel > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td {
  border-bottom: 0;
}
.panel > .table-responsive {
  border: 0;
  margin-bottom: 0;
}
.panel-heading {
  border-bottom: 1px solid transparent;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel-heading:before,
.panel-heading:after {
  content: " ";
  display: table;
}
.panel-heading:after {
  clear: both;
}
.panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.panel-heading > .panel-title {
  float: left;
  padding: 10px 15px;
}
.panel-heading > .panel-title > * {
  margin: 0;
}
.panel-heading > .panel-title > span {
  font-weight: normal;
}
.panel-heading > .panel-options {
  float: right;
  padding-right: 15px;
}
.panel-heading > .panel-options > a {
  margin-top: 10px;
}
.panel-heading > .panel-options > a[rel="reload"] > i {
  -webkit-transform-origin: 50%;
  -moz-transform-origin: 50%;
  -ms-transform-origin: 50%;
  -o-transform-origin: 50%;
  transform-origin: 50%;
}
.panel-heading > .panel-options > a[rel="collapse"] > i {
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-heading > .panel-options > .nav-tabs {
  margin-top: 0;
  margin-bottom: 0;
}
.panel-heading > .panel-options > .nav-tabs > li {
  margin-right: 4px;
}
.panel-heading > .panel-options > .nav-tabs > li:last-child {
  margin-right: 0;
}
lesshat-selector {
  -lh-property: 0; } 
@-webkit-keyframes reloadingAnimation{ 0%{ -webkit-transform: rotate(0deg); } 100%{ -webkit-transform: rotate(360deg); }}}
@-moz-keyframes reloadingAnimation{ 0%{ -moz-transform: rotate(0deg); } 100%{ -moz-transform: rotate(360deg); }}}
@-o-keyframes reloadingAnimation{ 0%{ -o-transform: rotate(0deg); } 100%{ -o-transform: rotate(360deg); }}}
@keyframes reloadingAnimation{ 0%{-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-ms-transform: rotate(0deg);transform: rotate(0deg); } 100%{-webkit-transform: rotate(360deg);-moz-transform: rotate(360deg);-ms-transform: rotate(360deg);transform: rotate(360deg); }};
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 14px;
}
.panel-title > a {
  color: inherit;
}
.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}
.panel-group {
  margin-bottom: 17px;
}
.panel-group .panel {
  margin-bottom: 0;
  overflow: hidden;
}
.panel-group .panel > .panel-heading > .panel-title {
  float: none;
}
.panel-group .panel > .panel-heading > .panel-title > a {
  display: block;
}
.panel-group .panel + .panel {
  margin-top: 5px;
}
.panel-group .panel-heading {
  border-bottom: 0;
}
.panel-group .panel-heading + .panel-collapse .panel-body {
  border-top: 1px solid #ddd;
}
.panel-group .panel-footer {
  border-top: 0;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #ddd;
}
.panel-group.joined > .panel {
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
}
.panel-group.joined > .panel + .panel {
  margin-top: 0;
  border-top: 0;
}
.panel-group.joined > .panel > .panel-heading {
  background-color: #ffffff;
}
.panel-group.joined > .panel > .panel-heading h4 a:before {
  position: relative;
  content: '\e87a';
  display: inline-block;
  font-family: "Entypo";
  color: rgba(115, 120, 129, 0.7);
  padding: 10px 15px;
  padding: 0;
  float: right;
  font-size: 17px;
  margin-left: 13px;
  top: 0px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-group.joined > .panel > .panel-heading h4 a.collapsed:before {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.panel-group.joined > .panel > .panel-heading + .panel-collapse {
  background-color: #f5f5f6;
  margin-bottom: 0;
  font-size: 12px;
}
.panel-group.joined > .panel:first-child {
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.panel-group.joined > .panel:last-child {
  -webkit-border-radius: 0 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 3px;
  background-clip: padding-box;
}
.panel-primary {
  border-color: #ebebeb;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-primary > .panel-heading {
  color: #373e4a;
  background-color: #ffffff;
  border-color: #ebebeb;
  padding: 0;
}
.panel-primary > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ebebeb;
}
.panel-primary > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-primary > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-primary > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #373e4a;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > a.bg {
  background-color: #f8f8f8;
  margin-left: 5px;
}
.panel-primary > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffffff;
}
.panel-primary > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ebebeb;
  padding-top: 5px;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ebebeb;
  background-color: #ebebeb;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffffff;
  background-color: #ffffff;
  color: #373e4a;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-primary > .panel-footer {
  background-color: #ffffff;
  color: #373e4a;
  border-top-color: #ebebeb;
}
.panel-primary > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ebebeb;
}
.panel-primary > .panel-body + .panel-body {
  border-top-color: #ebebeb;
}
.panel-invert {
  border-color: #39414e;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-invert > .panel-heading {
  color: #fff;
  background-color: #303641;
  border-color: #39414e;
  padding: 0;
}
.panel-invert > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #39414e;
}
.panel-invert > .panel-heading > .dropdown .caret {
  border-color: #fff transparent;
}
.panel-invert > .panel-heading > .panel-title > a {
  color: #fff;
}
.panel-invert > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #fff;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > a.bg {
  background-color: #4f5a6b;
  margin-left: 5px;
}
.panel-invert > .panel-heading > .panel-options > a.bg:hover {
  background-color: #556174;
}
.panel-invert > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #39414e;
  padding-top: 5px;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #39414e;
  background-color: #39414e;
  padding: 8px 10px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #303641;
  background-color: #303641;
  color: #fff;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #fff;
}
.panel-invert > .panel-footer {
  background-color: #303641;
  color: #fff;
  border-top-color: #39414e;
}
.panel-invert > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #39414e;
}
.panel-invert > .panel-body + .panel-body {
  border-top-color: #39414e;
}
.panel-invert > .panel-body {
  background-color: #303641;
  color: #778193;
}
.panel-default {
  border-color: #ebebeb;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-default > .panel-heading {
  color: #373e4a;
  background-color: #f0f0f1;
  border-color: #ebebeb;
  padding: 0;
}
.panel-default > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ebebeb;
}
.panel-default > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-default > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-default > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #373e4a;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > a.bg {
  background-color: #dedede;
  margin-left: 5px;
}
.panel-default > .panel-heading > .panel-options > a.bg:hover {
  background-color: #e6e6e6;
}
.panel-default > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ebebeb;
  padding-top: 5px;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ebebeb;
  background-color: #ebebeb;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #f0f0f1;
  background-color: #f0f0f1;
  color: #373e4a;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-default > .panel-footer {
  background-color: #f0f0f1;
  color: #373e4a;
  border-top-color: #ebebeb;
}
.panel-default > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ebebeb;
}
.panel-default > .panel-body + .panel-body {
  border-top-color: #ebebeb;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #dedede;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-success {
  border-color: #b4e8a8;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-success > .panel-heading {
  color: #045702;
  background-color: #bdedbc;
  border-color: #b4e8a8;
  padding: 0;
}
.panel-success > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #b4e8a8;
}
.panel-success > .panel-heading > .dropdown .caret {
  border-color: #045702 transparent;
}
.panel-success > .panel-heading > .panel-title > a {
  color: #045702;
}
.panel-success > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #045702;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > a.bg {
  background-color: #a2e294;
  margin-left: 5px;
}
.panel-success > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ade5a0;
}
.panel-success > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #b4e8a8;
  padding-top: 5px;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #b4e8a8;
  background-color: #b4e8a8;
  padding: 8px 10px;
  color: rgba(4, 87, 2, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #bdedbc;
  background-color: #bdedbc;
  color: #045702;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #045702;
}
.panel-success > .panel-footer {
  background-color: #bdedbc;
  color: #045702;
  border-top-color: #b4e8a8;
}
.panel-success > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #b4e8a8;
}
.panel-success > .panel-body + .panel-body {
  border-top-color: #b4e8a8;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #91dd80;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-warning {
  border-color: #ffd78a;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-warning > .panel-heading {
  color: #574802;
  background-color: #ffefa4;
  border-color: #ffd78a;
  padding: 0;
}
.panel-warning > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ffd78a;
}
.panel-warning > .panel-heading > .dropdown .caret {
  border-color: #574802 transparent;
}
.panel-warning > .panel-heading > .panel-title > a {
  color: #574802;
}
.panel-warning > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #574802;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > a.bg {
  background-color: #ffce71;
  margin-left: 5px;
}
.panel-warning > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffd480;
}
.panel-warning > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ffd78a;
  padding-top: 5px;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ffd78a;
  background-color: #ffd78a;
  padding: 8px 10px;
  color: rgba(87, 72, 2, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffefa4;
  background-color: #ffefa4;
  color: #574802;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #574802;
}
.panel-warning > .panel-footer {
  background-color: #ffefa4;
  color: #574802;
  border-top-color: #ffd78a;
}
.panel-warning > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ffd78a;
}
.panel-warning > .panel-body + .panel-body {
  border-top-color: #ffd78a;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #ffe258;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-danger {
  border-color: #ffafbd;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-danger > .panel-heading {
  color: #ac1818;
  background-color: #ffc9c9;
  border-color: #ffafbd;
  padding: 0;
}
.panel-danger > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ffafbd;
}
.panel-danger > .panel-heading > .dropdown .caret {
  border-color: #ac1818 transparent;
}
.panel-danger > .panel-heading > .panel-title > a {
  color: #ac1818;
}
.panel-danger > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #ac1818;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > a.bg {
  background-color: #ff96a7;
  margin-left: 5px;
}
.panel-danger > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffa5b4;
}
.panel-danger > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ffafbd;
  padding-top: 5px;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ffafbd;
  background-color: #ffafbd;
  padding: 8px 10px;
  color: rgba(172, 24, 24, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffc9c9;
  background-color: #ffc9c9;
  color: #ac1818;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #ac1818;
}
.panel-danger > .panel-footer {
  background-color: #ffc9c9;
  color: #ac1818;
  border-top-color: #ffafbd;
}
.panel-danger > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ffafbd;
}
.panel-danger > .panel-body + .panel-body {
  border-top-color: #ffafbd;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #ff7c7c;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-info {
  border-color: #a6e8f3;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-info > .panel-heading {
  color: #2c7ea1;
  background-color: #c5e8f7;
  border-color: #a6e8f3;
  padding: 0;
}
.panel-info > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #a6e8f3;
}
.panel-info > .panel-heading > .dropdown .caret {
  border-color: #2c7ea1 transparent;
}
.panel-info > .panel-heading > .panel-title > a {
  color: #2c7ea1;
}
.panel-info > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #2c7ea1;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > a.bg {
  background-color: #8fe3f0;
  margin-left: 5px;
}
.panel-info > .panel-heading > .panel-options > a.bg:hover {
  background-color: #9de6f1;
}
.panel-info > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #a6e8f3;
  padding-top: 5px;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #a6e8f3;
  background-color: #a6e8f3;
  padding: 8px 10px;
  color: rgba(44, 126, 161, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #c5e8f7;
  background-color: #c5e8f7;
  color: #2c7ea1;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #2c7ea1;
}
.panel-info > .panel-footer {
  background-color: #c5e8f7;
  color: #2c7ea1;
  border-top-color: #a6e8f3;
}
.panel-info > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #a6e8f3;
}
.panel-info > .panel-body + .panel-body {
  border-top-color: #a6e8f3;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #82cdee;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-dark {
  border-color: #222;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-dark > .panel-heading {
  color: #FFF;
  background-color: #333;
  border-color: #222;
  padding: 0;
}
.panel-dark > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #222;
}
.panel-dark > .panel-heading > .dropdown .caret {
  border-color: #FFF transparent;
}
.panel-dark > .panel-heading > .panel-title > a {
  color: #FFF;
}
.panel-dark > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #FFF;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > a.bg {
  background-color: #484848;
  margin-left: 5px;
}
.panel-dark > .panel-heading > .panel-options > a.bg:hover {
  background-color: #505050;
}
.panel-dark > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #222;
  padding-top: 5px;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #222;
  background-color: #222;
  padding: 8px 10px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #333;
  background-color: #333;
  color: #FFF;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #FFF;
}
.panel-dark > .panel-footer {
  background-color: #333;
  color: #FFF;
  border-top-color: #222;
}
.panel-dark > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #222;
}
.panel-dark > .panel-body + .panel-body {
  border-top-color: #222;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: rgba(255, 255, 255, 0.1);
  color: #FFF;
  border-color: rgba(34, 34, 34, 0.1);
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
  color: #222;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a i {
  color: #222;
}
.panel-gray {
  border-color: #EEE;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-gray > .panel-heading {
  color: #373e4a;
  background-color: #EEE;
  border-color: #FFF;
  padding: 0;
}
.panel-gray > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #EEE;
}
.panel-gray > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-gray > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-gray > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #373e4a;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > a.bg {
  background-color: #e0e0e0;
  margin-left: 5px;
}
.panel-gray > .panel-heading > .panel-options > a.bg:hover {
  background-color: #e8e8e8;
}
.panel-gray > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #EEE;
  padding-top: 5px;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #EEE;
  background-color: #EEE;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #EEE;
  background-color: #EEE;
  color: #373e4a;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-gray > .panel-footer {
  background-color: #EEE;
  color: #373e4a;
  border-top-color: #FFF;
}
.panel-gray > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #EEE;
}
.panel-gray > .panel-body + .panel-body {
  border-top-color: #FFF;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #FFF;
  border-bottom: 1px solid #FFF;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #EEE;
  border-color: #FFF;
  border-bottom-color: #EEE;
}
.panel-gray > .panel-body {
  background-color: #EEE;
}
.panel-gray > .panel-body + .panel-body {
  border-top-color: #FFF;
}
.panel-gradient {
  border-color: #CCC;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-gradient > .panel-heading {
  color: #303641;
  background-color: #FFF;
  border-color: #CCC;
  padding: 0;
}
.panel-gradient > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #CCC;
}
.panel-gradient > .panel-heading > .dropdown .caret {
  border-color: #303641 transparent;
}
.panel-gradient > .panel-heading > .panel-title > a {
  color: #303641;
}
.panel-gradient > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #303641;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > a.bg {
  background-color: #d1d1d1;
  margin-left: 5px;
}
.panel-gradient > .panel-heading > .panel-options > a.bg:hover {
  background-color: #d9d9d9;
}
.panel-gradient > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #CCC;
  padding-top: 5px;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #CCC;
  background-color: #CCC;
  padding: 8px 10px;
  color: rgba(48, 54, 65, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #FFF;
  background-color: #FFF;
  color: #303641;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #303641;
}
.panel-gradient > .panel-footer {
  background-color: #FFF;
  color: #303641;
  border-top-color: #CCC;
}
.panel-gradient > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #CCC;
}
.panel-gradient > .panel-body + .panel-body {
  border-top-color: #CCC;
}
.panel-gradient > .panel-heading {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMSAxIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIj48bGluZWFyR3JhZGllbnQgaWQ9Imxlc3NoYXQtZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNmZmZmZmYiIHN0b3Atb3BhY2l0eT0iMSIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI2U1ZTVlNSIgc3RvcC1vcGFjaXR5PSIxIi8+PC9saW5lYXJHcmFkaWVudD48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
  background-image: -webkit-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: -moz-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: -o-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: linear-gradient(to bottom, #ffffff 0%, #e5e5e5 100%);
}
.panel-gradient > .panel-heading > .panel-options i {
  color: #303641;
}
.panel-primary {
  border-color: #ebebeb;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-primary > .panel-heading {
  color: #373e4a;
  background-color: #ffffff;
  border-color: #ebebeb;
  padding: 0;
}
.panel-primary > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ebebeb;
}
.panel-primary > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-primary > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-primary > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #373e4a;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > a.bg {
  background-color: #f8f8f8;
  margin-left: 5px;
}
.panel-primary > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffffff;
}
.panel-primary > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ebebeb;
  padding-top: 5px;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ebebeb;
  background-color: #ebebeb;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffffff;
  background-color: #ffffff;
  color: #373e4a;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-primary > .panel-footer {
  background-color: #ffffff;
  color: #373e4a;
  border-top-color: #ebebeb;
}
.panel-primary > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ebebeb;
}
.panel-primary > .panel-body + .panel-body {
  border-top-color: #ebebeb;
}
.panel-invert {
  border-color: #39414e;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-invert > .panel-heading {
  color: #fff;
  background-color: #303641;
  border-color: #39414e;
  padding: 0;
}
.panel-invert > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #39414e;
}
.panel-invert > .panel-heading > .dropdown .caret {
  border-color: #fff transparent;
}
.panel-invert > .panel-heading > .panel-title > a {
  color: #fff;
}
.panel-invert > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #fff;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > a.bg {
  background-color: #4f5a6b;
  margin-left: 5px;
}
.panel-invert > .panel-heading > .panel-options > a.bg:hover {
  background-color: #556174;
}
.panel-invert > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #39414e;
  padding-top: 5px;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #39414e;
  background-color: #39414e;
  padding: 8px 10px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #303641;
  background-color: #303641;
  color: #fff;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #fff;
}
.panel-invert > .panel-footer {
  background-color: #303641;
  color: #fff;
  border-top-color: #39414e;
}
.panel-invert > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #39414e;
}
.panel-invert > .panel-body + .panel-body {
  border-top-color: #39414e;
}
.panel-invert > .panel-body {
  background-color: #303641;
  color: #778193;
}
.panel-default {
  border-color: #ebebeb;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-default > .panel-heading {
  color: #373e4a;
  background-color: #f0f0f1;
  border-color: #ebebeb;
  padding: 0;
}
.panel-default > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ebebeb;
}
.panel-default > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-default > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-default > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #373e4a;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > a.bg {
  background-color: #dedede;
  margin-left: 5px;
}
.panel-default > .panel-heading > .panel-options > a.bg:hover {
  background-color: #e6e6e6;
}
.panel-default > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ebebeb;
  padding-top: 5px;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ebebeb;
  background-color: #ebebeb;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #f0f0f1;
  background-color: #f0f0f1;
  color: #373e4a;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-default > .panel-footer {
  background-color: #f0f0f1;
  color: #373e4a;
  border-top-color: #ebebeb;
}
.panel-default > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ebebeb;
}
.panel-default > .panel-body + .panel-body {
  border-top-color: #ebebeb;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #dedede;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-success {
  border-color: #b4e8a8;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-success > .panel-heading {
  color: #045702;
  background-color: #bdedbc;
  border-color: #b4e8a8;
  padding: 0;
}
.panel-success > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #b4e8a8;
}
.panel-success > .panel-heading > .dropdown .caret {
  border-color: #045702 transparent;
}
.panel-success > .panel-heading > .panel-title > a {
  color: #045702;
}
.panel-success > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #045702;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > a.bg {
  background-color: #a2e294;
  margin-left: 5px;
}
.panel-success > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ade5a0;
}
.panel-success > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #b4e8a8;
  padding-top: 5px;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #b4e8a8;
  background-color: #b4e8a8;
  padding: 8px 10px;
  color: rgba(4, 87, 2, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #bdedbc;
  background-color: #bdedbc;
  color: #045702;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #045702;
}
.panel-success > .panel-footer {
  background-color: #bdedbc;
  color: #045702;
  border-top-color: #b4e8a8;
}
.panel-success > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #b4e8a8;
}
.panel-success > .panel-body + .panel-body {
  border-top-color: #b4e8a8;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #91dd80;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-warning {
  border-color: #ffd78a;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-warning > .panel-heading {
  color: #574802;
  background-color: #ffefa4;
  border-color: #ffd78a;
  padding: 0;
}
.panel-warning > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ffd78a;
}
.panel-warning > .panel-heading > .dropdown .caret {
  border-color: #574802 transparent;
}
.panel-warning > .panel-heading > .panel-title > a {
  color: #574802;
}
.panel-warning > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #574802;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > a.bg {
  background-color: #ffce71;
  margin-left: 5px;
}
.panel-warning > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffd480;
}
.panel-warning > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ffd78a;
  padding-top: 5px;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ffd78a;
  background-color: #ffd78a;
  padding: 8px 10px;
  color: rgba(87, 72, 2, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffefa4;
  background-color: #ffefa4;
  color: #574802;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #574802;
}
.panel-warning > .panel-footer {
  background-color: #ffefa4;
  color: #574802;
  border-top-color: #ffd78a;
}
.panel-warning > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ffd78a;
}
.panel-warning > .panel-body + .panel-body {
  border-top-color: #ffd78a;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #ffe258;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-danger {
  border-color: #ffafbd;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-danger > .panel-heading {
  color: #ac1818;
  background-color: #ffc9c9;
  border-color: #ffafbd;
  padding: 0;
}
.panel-danger > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ffafbd;
}
.panel-danger > .panel-heading > .dropdown .caret {
  border-color: #ac1818 transparent;
}
.panel-danger > .panel-heading > .panel-title > a {
  color: #ac1818;
}
.panel-danger > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #ac1818;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > a.bg {
  background-color: #ff96a7;
  margin-left: 5px;
}
.panel-danger > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffa5b4;
}
.panel-danger > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ffafbd;
  padding-top: 5px;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ffafbd;
  background-color: #ffafbd;
  padding: 8px 10px;
  color: rgba(172, 24, 24, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffc9c9;
  background-color: #ffc9c9;
  color: #ac1818;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #ac1818;
}
.panel-danger > .panel-footer {
  background-color: #ffc9c9;
  color: #ac1818;
  border-top-color: #ffafbd;
}
.panel-danger > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ffafbd;
}
.panel-danger > .panel-body + .panel-body {
  border-top-color: #ffafbd;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #ff7c7c;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-info {
  border-color: #a6e8f3;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-info > .panel-heading {
  color: #2c7ea1;
  background-color: #c5e8f7;
  border-color: #a6e8f3;
  padding: 0;
}
.panel-info > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #a6e8f3;
}
.panel-info > .panel-heading > .dropdown .caret {
  border-color: #2c7ea1 transparent;
}
.panel-info > .panel-heading > .panel-title > a {
  color: #2c7ea1;
}
.panel-info > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #2c7ea1;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > a.bg {
  background-color: #8fe3f0;
  margin-left: 5px;
}
.panel-info > .panel-heading > .panel-options > a.bg:hover {
  background-color: #9de6f1;
}
.panel-info > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #a6e8f3;
  padding-top: 5px;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #a6e8f3;
  background-color: #a6e8f3;
  padding: 8px 10px;
  color: rgba(44, 126, 161, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #c5e8f7;
  background-color: #c5e8f7;
  color: #2c7ea1;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #2c7ea1;
}
.panel-info > .panel-footer {
  background-color: #c5e8f7;
  color: #2c7ea1;
  border-top-color: #a6e8f3;
}
.panel-info > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #a6e8f3;
}
.panel-info > .panel-body + .panel-body {
  border-top-color: #a6e8f3;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #82cdee;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-dark {
  border-color: #222;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-dark > .panel-heading {
  color: #FFF;
  background-color: #333;
  border-color: #222;
  padding: 0;
}
.panel-dark > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #222;
}
.panel-dark > .panel-heading > .dropdown .caret {
  border-color: #FFF transparent;
}
.panel-dark > .panel-heading > .panel-title > a {
  color: #FFF;
}
.panel-dark > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #FFF;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > a.bg {
  background-color: #484848;
  margin-left: 5px;
}
.panel-dark > .panel-heading > .panel-options > a.bg:hover {
  background-color: #505050;
}
.panel-dark > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #222;
  padding-top: 5px;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #222;
  background-color: #222;
  padding: 8px 10px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #333;
  background-color: #333;
  color: #FFF;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #FFF;
}
.panel-dark > .panel-footer {
  background-color: #333;
  color: #FFF;
  border-top-color: #222;
}
.panel-dark > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #222;
}
.panel-dark > .panel-body + .panel-body {
  border-top-color: #222;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: rgba(255, 255, 255, 0.1);
  color: #FFF;
  border-color: rgba(34, 34, 34, 0.1);
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
  color: #222;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a i {
  color: #222;
}
.panel-gray {
  border-color: #EEE;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-gray > .panel-heading {
  color: #373e4a;
  background-color: #EEE;
  border-color: #FFF;
  padding: 0;
}
.panel-gray > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #EEE;
}
.panel-gray > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-gray > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-gray > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #373e4a;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > a.bg {
  background-color: #e0e0e0;
  margin-left: 5px;
}
.panel-gray > .panel-heading > .panel-options > a.bg:hover {
  background-color: #e8e8e8;
}
.panel-gray > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #EEE;
  padding-top: 5px;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #EEE;
  background-color: #EEE;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #EEE;
  background-color: #EEE;
  color: #373e4a;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-gray > .panel-footer {
  background-color: #EEE;
  color: #373e4a;
  border-top-color: #FFF;
}
.panel-gray > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #EEE;
}
.panel-gray > .panel-body + .panel-body {
  border-top-color: #FFF;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #FFF;
  border-bottom: 1px solid #FFF;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #EEE;
  border-color: #FFF;
  border-bottom-color: #EEE;
}
.panel-gray > .panel-body {
  background-color: #EEE;
}
.panel-gray > .panel-body + .panel-body {
  border-top-color: #FFF;
}
.panel-gradient {
  border-color: #CCC;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-gradient > .panel-heading {
  color: #303641;
  background-color: #FFF;
  border-color: #CCC;
  padding: 0;
}
.panel-gradient > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #CCC;
}
.panel-gradient > .panel-heading > .dropdown .caret {
  border-color: #303641 transparent;
}
.panel-gradient > .panel-heading > .panel-title > a {
  color: #303641;
}
.panel-gradient > .panel-heading > .panel-options > a {
  display: inline-block;
  color: #303641;
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > a.bg {
  background-color: #d1d1d1;
  margin-left: 5px;
}
.panel-gradient > .panel-heading > .panel-options > a.bg:hover {
  background-color: #d9d9d9;
}
.panel-gradient > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #CCC;
  padding-top: 5px;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #CCC;
  background-color: #CCC;
  padding: 8px 10px;
  color: rgba(48, 54, 65, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #FFF;
  background-color: #FFF;
  color: #303641;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #303641;
}
.panel-gradient > .panel-footer {
  background-color: #FFF;
  color: #303641;
  border-top-color: #CCC;
}
.panel-gradient > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #CCC;
}
.panel-gradient > .panel-body + .panel-body {
  border-top-color: #CCC;
}
.panel-gradient > .panel-heading {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMSAxIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIj48bGluZWFyR3JhZGllbnQgaWQ9Imxlc3NoYXQtZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNmZmZmZmYiIHN0b3Atb3BhY2l0eT0iMSIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI2U1ZTVlNSIgc3RvcC1vcGFjaXR5PSIxIi8+PC9saW5lYXJHcmFkaWVudD48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
  background-image: -webkit-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: -moz-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: -o-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: linear-gradient(to bottom, #ffffff 0%, #e5e5e5 100%);
}
.panel-gradient > .panel-heading > .panel-options i {
  color: #303641;
}
table {
  max-width: 100%;
  background-color: transparent;
}
th {
  text-align: left;
  font-weight: 400;
  color: #303641;
}
.table-bordered {
  border: 1px solid #ebebeb;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #ebebeb;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  background-color: #f5f5f6;
  border-bottom-width: 0px;
  color: #a6a7aa;
  border-bottom: 0 !important;
}
.table-bordered > tfoot > tr > th,
.table-bordered > tfoot > tr > td {
  background-color: #f5f5f6;
  border-top-width: 1px;
  color: #a6a7aa;
}
table > tbody > tr.highlight > td,
table > tbody > tr.highlight > th {
  background-color: #f1f2f4 !important;
  color: #303641;
}
.badge {
  background-color: #ebebeb;
  color: #373e4a;
}
.badge.badge-primary {
  background-color: #303641;
  color: #fff;
}
.badge.badge-secondary {
  background-color: #ee4749;
  color: #fff;
}
.badge.badge-success {
  background-color: #00a651;
  color: #fff;
}
.badge.badge-info {
  background-color: #21a9e1;
  color: #fff;
}
.badge.badge-warning {
  background-color: #fad839;
  color: #fff;
}
.badge.badge-danger {
  background-color: #cc2424;
  color: #fff;
}
.badge.badge-roundless {
  -webkit-border-radius: 1px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 1px;
  -moz-background-clip: padding;
  border-radius: 1px;
  background-clip: padding-box;
}
.badge:empty {
  display: none;
}
.nav-tabs {
  border-bottom: 1px solid #ddd;
  margin-top: 20px;
  margin-bottom: 10px;
}
.nav-tabs.right-aligned {
  text-align: right;
}
.nav-tabs.right-aligned > li {
  float: none;
  display: inline-block;
}
.nav-tabs.right-aligned > li > a {
  margin-left: 4px;
  margin-right: 0;
}
.nav-tabs.right-aligned > li:last-child > a {
  margin-right: 10px;
}
.nav-tabs.bordered {
  margin-bottom: 0;
}
.nav-tabs.bordered > li:first-child > a {
  margin-left: 0;
}
.nav-tabs.bordered > li:last-child > a {
  margin-right: 0;
}
.nav-tabs.bordered + .tab-content {
  border: 1px solid #ddd;
  border-top: 0;
  -webkit-border-radius: 0 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 3px;
  background-clip: padding-box;
  padding: 10px 15px;
  margin-bottom: 20px;
}
.nav-tabs.bordered + .tab-content > p:last-child {
  margin-bottom: 0;
}
.panel-group .panel > .panel-heading {
  padding: 10px 0px;
}
.panel-group .panel > .panel-heading > .panel-title > a {
  font-size: 15px;
}
.tooltip.tooltip-primary .tooltip-inner {
  background-color: #303641;
  color: #fff;
}
.tooltip.tooltip-primary.top .tooltip-arrow {
  border-top-color: #303641;
}
.tooltip.tooltip-primary.top-left .tooltip-arrow {
  border-top-color: #303641;
}
.tooltip.tooltip-primary.top-right .tooltip-arrow {
  border-top-color: #303641;
}
.tooltip.tooltip-primary.right .tooltip-arrow {
  border-right-color: #303641;
}
.tooltip.tooltip-primary.left .tooltip-arrow {
  border-left-color: #303641;
}
.tooltip.tooltip-primary.bottom .tooltip-arrow {
  border-bottom-color: #303641;
}
.tooltip.tooltip-primary.bottom-left .tooltip-arrow {
  border-bottom-color: #303641;
}
.tooltip.tooltip-primary.bottom-right .tooltip-arrow {
  border-bottom-color: #303641;
}
.tooltip.tooltip-secondary .tooltip-inner {
  background-color: #ec5956;
  color: #fff;
}
.tooltip.tooltip-secondary.top .tooltip-arrow {
  border-top-color: #ec5956;
}
.tooltip.tooltip-secondary.top-left .tooltip-arrow {
  border-top-color: #ec5956;
}
.tooltip.tooltip-secondary.top-right .tooltip-arrow {
  border-top-color: #ec5956;
}
.tooltip.tooltip-secondary.right .tooltip-arrow {
  border-right-color: #ec5956;
}
.tooltip.tooltip-secondary.left .tooltip-arrow {
  border-left-color: #ec5956;
}
.tooltip.tooltip-secondary.bottom .tooltip-arrow {
  border-bottom-color: #ec5956;
}
.tooltip.tooltip-secondary.bottom-left .tooltip-arrow {
  border-bottom-color: #ec5956;
}
.tooltip.tooltip-secondary.bottom-right .tooltip-arrow {
  border-bottom-color: #ec5956;
}
.tooltip.tooltip-default .tooltip-inner {
  background-color: #ebebeb;
  color: #303641;
}
.tooltip.tooltip-default.top .tooltip-arrow {
  border-top-color: #ebebeb;
}
.tooltip.tooltip-default.top-left .tooltip-arrow {
  border-top-color: #ebebeb;
}
.tooltip.tooltip-default.top-right .tooltip-arrow {
  border-top-color: #ebebeb;
}
.tooltip.tooltip-default.right .tooltip-arrow {
  border-right-color: #ebebeb;
}
.tooltip.tooltip-default.left .tooltip-arrow {
  border-left-color: #ebebeb;
}
.tooltip.tooltip-default.bottom .tooltip-arrow {
  border-bottom-color: #ebebeb;
}
.tooltip.tooltip-default.bottom-left .tooltip-arrow {
  border-bottom-color: #ebebeb;
}
.tooltip.tooltip-default.bottom-right .tooltip-arrow {
  border-bottom-color: #ebebeb;
}
.popover {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.popover.popover-primary {
  background-color: #303641;
  color: #fff;
  border-color: #303641;
  /*&.top .arrow { border-top-color: @popover_bg; &:after { border-top-color: @popover_border; } }
	&.bottom .arrow { border-top-color: @popover_bg; &:after { border-bottom-color: @popover_border; } }
	&.left .arrow { border-right-color: @popover_bg; &:after { border-left-color: @popover_border; } }
	&.right .arrow { border-left-color: @popover_bg; &:after { border-right-color: @popover_border; } }
	*/
}
.popover.popover-primary .popover-title {
  background-color: #3b4250;
  border-bottom-color: #303641;
  color: #FFF;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.popover.popover-primary.top .arrow {
  border-top-color: #303641;
}
.popover.popover-primary.top .arrow:after {
  border-top-color: #303641;
}
.popover.popover-primary.bottom .arrow {
  border-bottom-color: #303641;
}
.popover.popover-primary.bottom .arrow:after {
  border-bottom-color: #303641;
}
.popover.popover-primary.left .arrow {
  border-left-color: #303641;
}
.popover.popover-primary.left .arrow:after {
  border-left-color: #303641;
}
.popover.popover-primary.right .arrow {
  border-right-color: #303641;
}
.popover.popover-primary.right .arrow:after {
  border-right-color: #303641;
}
.popover.popover-secondary {
  background-color: #ec5956;
  color: #fff;
  border-color: #ec5956;
  /*&.top .arrow { border-top-color: @popover_bg; &:after { border-top-color: @popover_border; } }
	&.bottom .arrow { border-top-color: @popover_bg; &:after { border-bottom-color: @popover_border; } }
	&.left .arrow { border-right-color: @popover_bg; &:after { border-left-color: @popover_border; } }
	&.right .arrow { border-left-color: @popover_bg; &:after { border-right-color: @popover_border; } }
	*/
}
.popover.popover-secondary .popover-title {
  background-color: #e9423f;
  border-bottom-color: #ec5956;
  color: #fff;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.popover.popover-secondary.top .arrow {
  border-top-color: #ec5956;
}
.popover.popover-secondary.top .arrow:after {
  border-top-color: #ec5956;
}
.popover.popover-secondary.bottom .arrow {
  border-bottom-color: #ec5956;
}
.popover.popover-secondary.bottom .arrow:after {
  border-bottom-color: #ec5956;
}
.popover.popover-secondary.left .arrow {
  border-left-color: #ec5956;
}
.popover.popover-secondary.left .arrow:after {
  border-left-color: #ec5956;
}
.popover.popover-secondary.right .arrow {
  border-right-color: #ec5956;
}
.popover.popover-secondary.right .arrow:after {
  border-right-color: #ec5956;
}
.popover.popover-default {
  background-color: #ebebeb;
  color: #373e4a;
  border-color: #ebebeb;
  /*&.top .arrow { border-top-color: @popover_bg; &:after { border-top-color: @popover_border; } }
	&.bottom .arrow { border-top-color: @popover_bg; &:after { border-bottom-color: @popover_border; } }
	&.left .arrow { border-right-color: @popover_bg; &:after { border-left-color: @popover_border; } }
	&.right .arrow { border-left-color: @popover_bg; &:after { border-right-color: @popover_border; } }
	*/
}
.popover.popover-default .popover-title {
  background-color: #d2d2d2;
  border-bottom-color: #ebebeb;
  color: #303641;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.popover.popover-default.top .arrow {
  border-top-color: #ebebeb;
}
.popover.popover-default.top .arrow:after {
  border-top-color: #ebebeb;
}
.popover.popover-default.bottom .arrow {
  border-bottom-color: #ebebeb;
}
.popover.popover-default.bottom .arrow:after {
  border-bottom-color: #ebebeb;
}
.popover.popover-default.left .arrow {
  border-left-color: #ebebeb;
}
.popover.popover-default.left .arrow:after {
  border-left-color: #ebebeb;
}
.popover.popover-default.right .arrow {
  border-right-color: #ebebeb;
}
.popover.popover-default.right .arrow:after {
  border-right-color: #ebebeb;
}
.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 17px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 3px;
}
.breadcrumb > li {
  display: inline-block;
}
.breadcrumb > li i {
  margin-right: 5px;
  color: #737881;
}
.breadcrumb > li a {
  color: #737881;
  font-weight: 400;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.breadcrumb > li a:hover {
  color: #464e5e;
}
.breadcrumb > li + li:before {
  content: "/\00a0";
  padding: 0 5px;
  color: #ccc;
}
.breadcrumb > .active {
  color: #999999;
  font-weight: 400;
}
.breadcrumb > .active > a {
  font-weight: 400;
}
.breadcrumb.bc-2,
.breadcrumb.breadcrumb-2 {
  background-color: transparent;
  border: 1px solid #ebebeb;
}
.breadcrumb.bc-3,
.breadcrumb.breadcrumb-3 {
  padding-left: 0;
  padding-right: 0;
  background-color: transparent;
}
.btn {
  outline: 0 !important;
}
.btn:active {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn.btn-icon {
  position: relative;
}
.btn.btn-icon i {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
}
.btn-default {
  color: #303641;
  background-color: #f0f0f1;
  border-color: #f0f0f1;
}
.btn-default:focus,
.btn-default.focus {
  color: #303641;
  background-color: #d6d6d8;
  border-color: #aeaeb3;
}
.btn-default:hover {
  color: #303641;
  background-color: #d6d6d8;
  border-color: #d0d0d3;
}
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  color: #303641;
  background-color: #d6d6d8;
  border-color: #d0d0d3;
}
.btn-default:active:hover,
.btn-default.active:hover,
.open > .dropdown-toggle.btn-default:hover,
.btn-default:active:focus,
.btn-default.active:focus,
.open > .dropdown-toggle.btn-default:focus,
.btn-default:active.focus,
.btn-default.active.focus,
.open > .dropdown-toggle.btn-default.focus {
  color: #303641;
  background-color: #c3c3c7;
  border-color: #aeaeb3;
}
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  background-image: none;
}
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus {
  background-color: #f0f0f1;
  border-color: #f0f0f1;
}
.btn-default .badge {
  color: #f0f0f1;
  background-color: #303641;
}
.btn-default:hover,
.btn-default:focus,
.btn-default.focus {
  color: #303641 !important;
}
.btn-default.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-default.btn-icon i {
  background-color: #dbdbdd;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-default.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-default.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-default.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-default.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-default.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-default.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-default.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-default.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-default.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-default.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-default.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-primary {
  color: #fff;
  background-color: #303641;
  border-color: #252a32;
}
.btn-primary:focus,
.btn-primary.focus {
  color: #fff;
  background-color: #1a1e24;
  border-color: #000000;
}
.btn-primary:hover {
  color: #fff;
  background-color: #1a1e24;
  border-color: #0b0d0f;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: #fff;
  background-color: #1a1e24;
  border-color: #0b0d0f;
}
.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus {
  color: #fff;
  background-color: #0b0d0f;
  border-color: #000000;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  background-image: none;
}
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus {
  background-color: #303641;
  border-color: #252a32;
}
.btn-primary .badge {
  color: #303641;
  background-color: #fff;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary.focus {
  color: #fff !important;
}
.btn-primary.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-primary.btn-icon i {
  background-color: #1f232a;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-primary.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-primary.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-primary.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-primary.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-primary.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-primary.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-primary.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-primary.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-primary.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-primary.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-primary.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-blue {
  color: #FFF;
  background-color: #0072bc;
  border-color: #0072bc;
}
.btn-blue:focus,
.btn-blue.focus {
  color: #FFF;
  background-color: #005389;
  border-color: #00253d;
}
.btn-blue:hover {
  color: #FFF;
  background-color: #005389;
  border-color: #004d7f;
}
.btn-blue:active,
.btn-blue.active,
.open > .dropdown-toggle.btn-blue {
  color: #FFF;
  background-color: #005389;
  border-color: #004d7f;
}
.btn-blue:active:hover,
.btn-blue.active:hover,
.open > .dropdown-toggle.btn-blue:hover,
.btn-blue:active:focus,
.btn-blue.active:focus,
.open > .dropdown-toggle.btn-blue:focus,
.btn-blue:active.focus,
.btn-blue.active.focus,
.open > .dropdown-toggle.btn-blue.focus {
  color: #FFF;
  background-color: #003d65;
  border-color: #00253d;
}
.btn-blue:active,
.btn-blue.active,
.open > .dropdown-toggle.btn-blue {
  background-image: none;
}
.btn-blue.disabled:hover,
.btn-blue[disabled]:hover,
fieldset[disabled] .btn-blue:hover,
.btn-blue.disabled:focus,
.btn-blue[disabled]:focus,
fieldset[disabled] .btn-blue:focus,
.btn-blue.disabled.focus,
.btn-blue[disabled].focus,
fieldset[disabled] .btn-blue.focus {
  background-color: #0072bc;
  border-color: #0072bc;
}
.btn-blue .badge {
  color: #0072bc;
  background-color: #FFF;
}
.btn-blue:hover,
.btn-blue:focus,
.btn-blue.focus {
  color: #FFF !important;
}
.btn-blue.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-blue.btn-icon i {
  background-color: #005993;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-blue.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-blue.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-blue.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-blue.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-blue.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-blue.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-blue.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-blue.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-blue.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-blue.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-blue.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-red {
  color: #FFF;
  background-color: #d42020;
  border-color: #d42020;
}
.btn-red:focus,
.btn-red.focus {
  color: #FFF;
  background-color: #a81919;
  border-color: #650f0f;
}
.btn-red:hover {
  color: #FFF;
  background-color: #a81919;
  border-color: #9f1818;
}
.btn-red:active,
.btn-red.active,
.open > .dropdown-toggle.btn-red {
  color: #FFF;
  background-color: #a81919;
  border-color: #9f1818;
}
.btn-red:active:hover,
.btn-red.active:hover,
.open > .dropdown-toggle.btn-red:hover,
.btn-red:active:focus,
.btn-red.active:focus,
.open > .dropdown-toggle.btn-red:focus,
.btn-red:active.focus,
.btn-red.active.focus,
.open > .dropdown-toggle.btn-red.focus {
  color: #FFF;
  background-color: #891515;
  border-color: #650f0f;
}
.btn-red:active,
.btn-red.active,
.open > .dropdown-toggle.btn-red {
  background-image: none;
}
.btn-red.disabled:hover,
.btn-red[disabled]:hover,
fieldset[disabled] .btn-red:hover,
.btn-red.disabled:focus,
.btn-red[disabled]:focus,
fieldset[disabled] .btn-red:focus,
.btn-red.disabled.focus,
.btn-red[disabled].focus,
fieldset[disabled] .btn-red.focus {
  background-color: #d42020;
  border-color: #d42020;
}
.btn-red .badge {
  color: #d42020;
  background-color: #FFF;
}
.btn-red:hover,
.btn-red:focus,
.btn-red.focus {
  color: #FFF !important;
}
.btn-red.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-red.btn-icon i {
  background-color: #b11b1b;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-red.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-red.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-red.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-red.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-red.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-red.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-red.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-red.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-red.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-red.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-red.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-orange {
  color: #FFF;
  background-color: #ff9600;
  border-color: #ff9600;
}
.btn-orange:focus,
.btn-orange.focus {
  color: #FFF;
  background-color: #cc7800;
  border-color: #804b00;
}
.btn-orange:hover {
  color: #FFF;
  background-color: #cc7800;
  border-color: #c27200;
}
.btn-orange:active,
.btn-orange.active,
.open > .dropdown-toggle.btn-orange {
  color: #FFF;
  background-color: #cc7800;
  border-color: #c27200;
}
.btn-orange:active:hover,
.btn-orange.active:hover,
.open > .dropdown-toggle.btn-orange:hover,
.btn-orange:active:focus,
.btn-orange.active:focus,
.open > .dropdown-toggle.btn-orange:focus,
.btn-orange:active.focus,
.btn-orange.active.focus,
.open > .dropdown-toggle.btn-orange.focus {
  color: #FFF;
  background-color: #a86300;
  border-color: #804b00;
}
.btn-orange:active,
.btn-orange.active,
.open > .dropdown-toggle.btn-orange {
  background-image: none;
}
.btn-orange.disabled:hover,
.btn-orange[disabled]:hover,
fieldset[disabled] .btn-orange:hover,
.btn-orange.disabled:focus,
.btn-orange[disabled]:focus,
fieldset[disabled] .btn-orange:focus,
.btn-orange.disabled.focus,
.btn-orange[disabled].focus,
fieldset[disabled] .btn-orange.focus {
  background-color: #ff9600;
  border-color: #ff9600;
}
.btn-orange .badge {
  color: #ff9600;
  background-color: #FFF;
}
.btn-orange:hover,
.btn-orange:focus,
.btn-orange.focus {
  color: #FFF !important;
}
.btn-orange.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-orange.btn-icon i {
  background-color: #d67e00;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-orange.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-orange.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-orange.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-orange.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-orange.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-orange.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-orange.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-orange.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-orange.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-orange.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-orange.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-gold {
  color: #846e20;
  background-color: #fcd036;
  border-color: #fcd036;
}
.btn-gold:focus,
.btn-gold.focus {
  color: #846e20;
  background-color: #fbc404;
  border-color: #b08903;
}
.btn-gold:hover {
  color: #846e20;
  background-color: #fbc404;
  border-color: #f1bc04;
}
.btn-gold:active,
.btn-gold.active,
.open > .dropdown-toggle.btn-gold {
  color: #846e20;
  background-color: #fbc404;
  border-color: #f1bc04;
}
.btn-gold:active:hover,
.btn-gold.active:hover,
.open > .dropdown-toggle.btn-gold:hover,
.btn-gold:active:focus,
.btn-gold.active:focus,
.open > .dropdown-toggle.btn-gold:focus,
.btn-gold:active.focus,
.btn-gold.active.focus,
.open > .dropdown-toggle.btn-gold.focus {
  color: #846e20;
  background-color: #d8a903;
  border-color: #b08903;
}
.btn-gold:active,
.btn-gold.active,
.open > .dropdown-toggle.btn-gold {
  background-image: none;
}
.btn-gold.disabled:hover,
.btn-gold[disabled]:hover,
fieldset[disabled] .btn-gold:hover,
.btn-gold.disabled:focus,
.btn-gold[disabled]:focus,
fieldset[disabled] .btn-gold:focus,
.btn-gold.disabled.focus,
.btn-gold[disabled].focus,
fieldset[disabled] .btn-gold.focus {
  background-color: #fcd036;
  border-color: #fcd036;
}
.btn-gold .badge {
  color: #fcd036;
  background-color: #846e20;
}
.btn-gold:hover,
.btn-gold:focus,
.btn-gold.focus {
  color: #846e20 !important;
}
.btn-gold.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-gold.btn-icon i {
  background-color: #fbc70e;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-gold.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-gold.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-gold.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-gold.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-gold.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-gold.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-gold.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-gold.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-gold.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-gold.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-gold.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-black {
  color: #FFF;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:focus,
.btn-black.focus {
  color: #FFF;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:hover {
  color: #FFF;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:active,
.btn-black.active,
.open > .dropdown-toggle.btn-black {
  color: #FFF;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:active:hover,
.btn-black.active:hover,
.open > .dropdown-toggle.btn-black:hover,
.btn-black:active:focus,
.btn-black.active:focus,
.open > .dropdown-toggle.btn-black:focus,
.btn-black:active.focus,
.btn-black.active.focus,
.open > .dropdown-toggle.btn-black.focus {
  color: #FFF;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:active,
.btn-black.active,
.open > .dropdown-toggle.btn-black {
  background-image: none;
}
.btn-black.disabled:hover,
.btn-black[disabled]:hover,
fieldset[disabled] .btn-black:hover,
.btn-black.disabled:focus,
.btn-black[disabled]:focus,
fieldset[disabled] .btn-black:focus,
.btn-black.disabled.focus,
.btn-black[disabled].focus,
fieldset[disabled] .btn-black.focus {
  background-color: #000000;
  border-color: #000000;
}
.btn-black .badge {
  color: #000000;
  background-color: #FFF;
}
.btn-black:hover,
.btn-black:focus,
.btn-black.focus {
  color: #FFF !important;
}
.btn-black.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-black.btn-icon i {
  background-color: #000000;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-black.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-black.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-black.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-black.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-black.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-black.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-black.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-black.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-black.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-black.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-black.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-white {
  color: #303641;
  background-color: #FFF;
  border-color: #FFF;
  border-color: #ebebeb !important;
}
.btn-white:focus,
.btn-white.focus {
  color: #303641;
  background-color: #e6e6e6;
  border-color: #bfbfbf;
}
.btn-white:hover {
  color: #303641;
  background-color: #e6e6e6;
  border-color: #e0e0e0;
}
.btn-white:active,
.btn-white.active,
.open > .dropdown-toggle.btn-white {
  color: #303641;
  background-color: #e6e6e6;
  border-color: #e0e0e0;
}
.btn-white:active:hover,
.btn-white.active:hover,
.open > .dropdown-toggle.btn-white:hover,
.btn-white:active:focus,
.btn-white.active:focus,
.open > .dropdown-toggle.btn-white:focus,
.btn-white:active.focus,
.btn-white.active.focus,
.open > .dropdown-toggle.btn-white.focus {
  color: #303641;
  background-color: #d4d4d4;
  border-color: #bfbfbf;
}
.btn-white:active,
.btn-white.active,
.open > .dropdown-toggle.btn-white {
  background-image: none;
}
.btn-white.disabled:hover,
.btn-white[disabled]:hover,
fieldset[disabled] .btn-white:hover,
.btn-white.disabled:focus,
.btn-white[disabled]:focus,
fieldset[disabled] .btn-white:focus,
.btn-white.disabled.focus,
.btn-white[disabled].focus,
fieldset[disabled] .btn-white.focus {
  background-color: #FFF;
  border-color: #FFF;
}
.btn-white .badge {
  color: #FFF;
  background-color: #303641;
}
.btn-white:hover,
.btn-white:focus,
.btn-white.focus {
  color: #303641 !important;
}
.btn-white.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-white.btn-icon i {
  background-color: #ebebeb;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-white.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-white.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-white.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-white.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-white.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-white.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-white.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-white.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-white.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-white.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-white.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-warning {
  color: #fff;
  background-color: #fad839;
  border-color: #f9d320;
}
.btn-warning:focus,
.btn-warning.focus {
  color: #fff;
  background-color: #f9ce07;
  border-color: #967c04;
}
.btn-warning:hover {
  color: #fff;
  background-color: #f9ce07;
  border-color: #d7b205;
}
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  color: #fff;
  background-color: #f9ce07;
  border-color: #d7b205;
}
.btn-warning:active:hover,
.btn-warning.active:hover,
.open > .dropdown-toggle.btn-warning:hover,
.btn-warning:active:focus,
.btn-warning.active:focus,
.open > .dropdown-toggle.btn-warning:focus,
.btn-warning:active.focus,
.btn-warning.active.focus,
.open > .dropdown-toggle.btn-warning.focus {
  color: #fff;
  background-color: #d7b205;
  border-color: #967c04;
}
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  background-image: none;
}
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus {
  background-color: #fad839;
  border-color: #f9d320;
}
.btn-warning .badge {
  color: #fad839;
  background-color: #fff;
}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning.focus {
  color: #fff !important;
}
.btn-warning.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-warning.btn-icon i {
  background-color: #f9d011;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-warning.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-warning.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-warning.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-warning.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-warning.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-warning.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-warning.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-warning.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-warning.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-warning.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-warning.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-danger {
  color: #fff;
  background-color: #cc2424;
  border-color: #b62020;
}
.btn-danger:focus,
.btn-danger.focus {
  color: #fff;
  background-color: #a11c1c;
  border-color: #4a0d0d;
}
.btn-danger:hover {
  color: #fff;
  background-color: #a11c1c;
  border-color: #821717;
}
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  color: #fff;
  background-color: #a11c1c;
  border-color: #821717;
}
.btn-danger:active:hover,
.btn-danger.active:hover,
.open > .dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open > .dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open > .dropdown-toggle.btn-danger.focus {
  color: #fff;
  background-color: #821717;
  border-color: #4a0d0d;
}
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  background-image: none;
}
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus {
  background-color: #cc2424;
  border-color: #b62020;
}
.btn-danger .badge {
  color: #cc2424;
  background-color: #fff;
}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger.focus {
  color: #fff !important;
}
.btn-danger.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-danger.btn-icon i {
  background-color: #a91e1e;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-danger.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-danger.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-danger.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-danger.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-danger.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-danger.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-danger.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-danger.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-danger.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-danger.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-danger.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-success,
.btn-green {
  color: #fff;
  background-color: #00a651;
  border-color: #008d45;
}
.btn-success:focus,
.btn-green:focus,
.btn-success.focus,
.btn-green.focus {
  color: #fff;
  background-color: #007338;
  border-color: #000d06;
}
.btn-success:hover,
.btn-green:hover {
  color: #fff;
  background-color: #007338;
  border-color: #004f27;
}
.btn-success:active,
.btn-green:active,
.btn-success.active,
.btn-green.active,
.open > .dropdown-toggle.btn-success,
.open > .dropdown-toggle.btn-green {
  color: #fff;
  background-color: #007338;
  border-color: #004f27;
}
.btn-success:active:hover,
.btn-green:active:hover,
.btn-success.active:hover,
.btn-green.active:hover,
.open > .dropdown-toggle.btn-success:hover,
.open > .dropdown-toggle.btn-green:hover,
.btn-success:active:focus,
.btn-green:active:focus,
.btn-success.active:focus,
.btn-green.active:focus,
.open > .dropdown-toggle.btn-success:focus,
.open > .dropdown-toggle.btn-green:focus,
.btn-success:active.focus,
.btn-green:active.focus,
.btn-success.active.focus,
.btn-green.active.focus,
.open > .dropdown-toggle.btn-success.focus,
.open > .dropdown-toggle.btn-green.focus {
  color: #fff;
  background-color: #004f27;
  border-color: #000d06;
}
.btn-success:active,
.btn-green:active,
.btn-success.active,
.btn-green.active,
.open > .dropdown-toggle.btn-success,
.open > .dropdown-toggle.btn-green {
  background-image: none;
}
.btn-success.disabled:hover,
.btn-green.disabled:hover,
.btn-success[disabled]:hover,
.btn-green[disabled]:hover,
fieldset[disabled] .btn-success:hover,
fieldset[disabled] .btn-green:hover,
.btn-success.disabled:focus,
.btn-green.disabled:focus,
.btn-success[disabled]:focus,
.btn-green[disabled]:focus,
fieldset[disabled] .btn-success:focus,
fieldset[disabled] .btn-green:focus,
.btn-success.disabled.focus,
.btn-green.disabled.focus,
.btn-success[disabled].focus,
.btn-green[disabled].focus,
fieldset[disabled] .btn-success.focus,
fieldset[disabled] .btn-green.focus {
  background-color: #00a651;
  border-color: #008d45;
}
.btn-success .badge,
.btn-green .badge {
  color: #00a651;
  background-color: #fff;
}
.btn-success:hover,
.btn-green:hover,
.btn-success:focus,
.btn-green:focus,
.btn-success.focus,
.btn-green.focus {
  color: #fff !important;
}
.btn-success.btn-icon,
.btn-green.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-success.btn-icon i,
.btn-green.btn-icon i {
  background-color: #007d3d;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-success.btn-icon.icon-left,
.btn-green.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-success.btn-icon.icon-left i,
.btn-green.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-success.btn-icon.btn-lg,
.btn-green.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-success.btn-icon.btn-lg.icon-left,
.btn-green.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-success.btn-icon.btn-lg i,
.btn-green.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-success.btn-icon.btn-sm,
.btn-green.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-success.btn-icon.btn-sm.icon-left,
.btn-green.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-success.btn-icon.btn-sm i,
.btn-green.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-success.btn-icon.btn-xs,
.btn-green.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-success.btn-icon.btn-xs.icon-left,
.btn-green.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-success.btn-icon.btn-xs i,
.btn-green.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-info {
  color: #fff;
  background-color: #21a9e1;
  border-color: #1c99cd;
}
.btn-info:focus,
.btn-info.focus {
  color: #fff;
  background-color: #1988b6;
  border-color: #0c455d;
}
.btn-info:hover {
  color: #fff;
  background-color: #1988b6;
  border-color: #147197;
}
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  color: #fff;
  background-color: #1988b6;
  border-color: #147197;
}
.btn-info:active:hover,
.btn-info.active:hover,
.open > .dropdown-toggle.btn-info:hover,
.btn-info:active:focus,
.btn-info.active:focus,
.open > .dropdown-toggle.btn-info:focus,
.btn-info:active.focus,
.btn-info.active.focus,
.open > .dropdown-toggle.btn-info.focus {
  color: #fff;
  background-color: #147197;
  border-color: #0c455d;
}
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  background-image: none;
}
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus {
  background-color: #21a9e1;
  border-color: #1c99cd;
}
.btn-info .badge {
  color: #21a9e1;
  background-color: #fff;
}
.btn-info:hover,
.btn-info:focus,
.btn-info.focus {
  color: #fff !important;
}
.btn-info.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-info.btn-icon i {
  background-color: #1a8fbf;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-info.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-info.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-info.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-info.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-info.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 3px;
}
.btn-info.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-info.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-info.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-info.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-info.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-info.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
.bs-example > .btn,
.bs-example > .make-switch,
.bs-example > .btn-group {
  margin-top: 4px;
  margin-bottom: 4px;
  margin-right: 9px;
}
.bs-example.bs-baseline-top .btn,
.bs-example.bs-baseline-top .btn-group {
  vertical-align: top !important;
}
.dropdown-menu {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.dropdown-menu.dropdown-green {
  background-color: #00a651;
  border-color: #009247;
}
.dropdown-menu.dropdown-green > li > a {
  color: #fff;
}
.dropdown-menu.dropdown-green > li:hover a,
.dropdown-menu.dropdown-green > li:active a {
  background-color: #008d45;
  color: #fff;
}
.dropdown-menu.dropdown-green .divider,
.dropdown-menu.dropdown-green .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #009247;
}
.dropdown-menu.dropdown-gold {
  background-color: #fcd036;
  border-color: #ecb804;
}
.dropdown-menu.dropdown-gold > li > a {
  color: #846e20;
}
.dropdown-menu.dropdown-gold > li:hover a,
.dropdown-menu.dropdown-gold > li:active a {
  background-color: #f1bc04;
  color: #846e20;
}
.dropdown-menu.dropdown-gold .divider,
.dropdown-menu.dropdown-gold .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #ecb804;
}
.dropdown-menu.dropdown-blue {
  background-color: #0072bc;
  border-color: #0063a3;
}
.dropdown-menu.dropdown-blue > li > a {
  color: #fff;
}
.dropdown-menu.dropdown-blue > li:hover a,
.dropdown-menu.dropdown-blue > li:active a {
  background-color: #0063a3;
  color: #fff;
}
.dropdown-menu.dropdown-blue .divider,
.dropdown-menu.dropdown-blue .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #0063a3;
}
.dropdown-menu.dropdown-primary {
  background-color: #303641;
  border-color: #454a54;
}
.dropdown-menu.dropdown-primary > li > a {
  color: #fff;
}
.dropdown-menu.dropdown-primary > li:hover a,
.dropdown-menu.dropdown-primary > li:active a {
  background-color: #252a32;
  color: #fff;
}
.dropdown-menu.dropdown-primary .divider,
.dropdown-menu.dropdown-primary .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #454a54;
}
.dropdown-menu.dropdown-red {
  background-color: #d42020;
  border-color: #b51b1b;
}
.dropdown-menu.dropdown-red > li > a {
  color: #fff;
}
.dropdown-menu.dropdown-red > li:hover a,
.dropdown-menu.dropdown-red > li:active a {
  background-color: #be1d1d;
  color: #fff;
}
.dropdown-menu.dropdown-red .divider,
.dropdown-menu.dropdown-red .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #b51b1b;
}
.dropdown-menu.dropdown-black {
  background-color: #000;
  border-color: #454545;
}
.dropdown-menu.dropdown-black > li > a {
  color: #fff;
}
.dropdown-menu.dropdown-black > li:hover a,
.dropdown-menu.dropdown-black > li:active a {
  background-color: #383838;
  color: #fff;
}
.dropdown-menu.dropdown-black .divider,
.dropdown-menu.dropdown-black .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #454545;
}
.label {
  display: inline-block;
  padding: 6px 12px;
  font-weight: normal;
}
.label + .label {
  margin-left: 10px;
}
.label-default {
  background-color: #ebebeb;
  color: #303641;
}
.label-default[href]:hover,
.label-default[href]:focus {
  background-color: #d2d2d2;
}
.label-primary {
  background-color: #373e4a;
  color: #fff;
}
.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #21252d;
}
.label-secondary {
  background-color: #ec5956;
}
.label-secondary[href]:hover,
.label-secondary[href]:focus {
  background-color: #e72c28;
}
.label-success {
  background-color: #00a651;
}
.label-success[href]:hover,
.label-success[href]:focus {
  background-color: #007338;
}
.label-info {
  background-color: #21a9e1;
}
.label-info[href]:hover,
.label-info[href]:focus {
  background-color: #1988b6;
}
.label-warning {
  background-color: #fad839;
}
.label-warning[href]:hover,
.label-warning[href]:focus {
  background-color: #f9ce07;
}
.label-danger {
  background-color: #cc2424;
}
.label-danger[href]:hover,
.label-danger[href]:focus {
  background-color: #a11c1c;
}
.jumbotron {
  padding: 40px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.progress {
  -webkit-border-radius: 10px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10px;
  -moz-background-clip: padding;
  border-radius: 10px;
  background-clip: padding-box;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.progress-bar {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.progress-bar-default {
  background: #fff;
  padding: 2px;
  border: 1px solid #ebebeb;
}
.progress-bar-default .progress-bar {
  background-color: #ebebeb;
  background-image: -webkit-linear-gradient(45deg, #e1e1e1 25%, transparent 25%, transparent 50%, #e1e1e1 50%, #e1e1e1 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, #e1e1e1 25%, transparent 25%, transparent 50%, #e1e1e1 50%, #e1e1e1 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #e1e1e1 25%, transparent 25%, transparent 50%, #e1e1e1 50%, #e1e1e1 75%, transparent 75%, transparent);
  -webkit-border-radius: 10px 0 0 10px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10px 0 0 10px;
  -moz-background-clip: padding;
  border-radius: 10px 0 0 10px;
  background-clip: padding-box;
}
.progress-striped .progress-bar-default .progress-bar {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-default .progress-bar[aria-valuenow="100"] {
  -webkit-border-radius: 10px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10px;
  -moz-background-clip: padding;
  border-radius: 10px;
  background-clip: padding-box;
}
html,
body {
  height: 100%;
  position: relative;
}
.page-container {
  width: 100%;
  display: table;
  height: 100%;
  table-layout: fixed;
}
@media screen and (max-width: 768px) {
  .page-container {
    display: block;
  }
}
.page-container.loaded .page-container {
  position: relative;
}
.page-container .sidebar-menu {
  display: table-cell;
  vertical-align: top;
  background: transparent;
  width: 280px;
  position: relative;
  z-index: 100;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
@media screen and (max-width: 767px) {
  .page-container .sidebar-menu {
    display: block;
  }
}
@media screen and (min-width: 768px) {
  .page-container .sidebar-menu.fixed .sidebar-menu-inner {
    position: fixed;
    height: auto;
    bottom: 0;
    width: 280px;
  }
}
.page-container .sidebar-menu .sidebar-menu-inner {
  position: relative;
}
.page-container .sidebar-menu .sidebar-menu-inner.ps-container {
  overflow: hidden;
  height: 100%;
}
.sidebar-collapsed.page-container .sidebar-menu .sidebar-menu-inner.ps-container {
  overflow: visible;
}
.page-container .sidebar-menu .logo-env {
  width: 100%;
  padding: 35px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.page-container .sidebar-menu .logo-env:before,
.page-container .sidebar-menu .logo-env:after {
  content: " ";
  display: table;
}
.page-container .sidebar-menu .logo-env:after {
  clear: both;
}
.page-container .sidebar-menu .logo-env > div {
  display: block;
  vertical-align: middle;
  white-space: nowrap;
  float: left;
}
.page-container .sidebar-menu .logo-env > div > a {
  display: inline-block;
  color: #aaabae;
}
.page-container .sidebar-menu .logo-env > div.sidebar-collapse,
.page-container .sidebar-menu .logo-env > div.sidebar-mobile-menu {
  position: relative;
  float: right;
}
.page-container .sidebar-menu .logo-env > div.sidebar-collapse a,
.page-container .sidebar-menu .logo-env > div.sidebar-mobile-menu a {
  display: inline-block;
  border: 1px solid #454a54;
  text-align: center;
  padding: 0;
  line-height: 1;
  font-size: 20px;
  font-weight: 300;
  padding: 5px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 200ms ease-in-out;
  -moz-transition: all 200ms ease-in-out;
  -o-transition: all 200ms ease-in-out;
  transition: all 200ms ease-in-out;
}
.page-container .sidebar-menu .logo-env > div.sidebar-collapse a:hover,
.page-container .sidebar-menu .logo-env > div.sidebar-mobile-menu a:hover {
  background-color: rgba(69, 74, 84, 0.4);
}
.page-container .sidebar-menu #main-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  margin-bottom: 20px;
}
.page-container .sidebar-menu #main-menu li {
  position: relative;
  margin: 0;
  font-size: 12px;
  border-bottom: 1px solid rgba(69, 74, 84, 0.7);
}
.page-container .sidebar-menu #main-menu li#search {
  background: #262b34;
  border-bottom-color: rgba(69, 74, 84, 0.5);
  position: relative;
  height: 48px;
}
.page-container .sidebar-menu #main-menu li#search ::-webkit-input-placeholder {
  color: rgba(170, 171, 174, 0.7);
}
.page-container .sidebar-menu #main-menu li#search :-moz-placeholder {
  color: rgba(170, 171, 174, 0.7);
}
.page-container .sidebar-menu #main-menu li#search ::-moz-placeholder {
  color: rgba(170, 171, 174, 0.7);
}
.page-container .sidebar-menu #main-menu li#search :-ms-input-placeholder {
  color: rgba(170, 171, 174, 0.7);
}
.page-container .sidebar-menu #main-menu li#search .search-input {
  background: none;
  padding: 0;
  margin: 0;
  border: none;
  outline: none;
  padding: 15px 20px;
  padding-right: 45px;
  width: 100%;
  color: #aaabae;
  z-index: 100;
  -webkit-transition: 350ms opacity ease-in-out;
  -moz-transition: 350ms opacity ease-in-out;
  -o-transition: 350ms opacity ease-in-out;
  transition: 350ms opacity ease-in-out;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.page-container .sidebar-menu #main-menu li#search button {
  position: absolute;
  right: 0;
  top: 0;
  background: none;
  padding: 0;
  margin: 0;
  border: none;
  outline: none;
  color: #aaabae;
  padding: 15px 20px;
  padding-top: 13px;
}
.page-container .sidebar-menu #main-menu li#search button i {
  margin: 0;
}
.page-container .sidebar-menu #main-menu li i {
  position: relative;
  font-size: 15px;
  margin-right: 5px;
}
.page-container .sidebar-menu #main-menu li a {
  position: relative;
  display: block;
  padding: 10px 20px;
  color: #aaabae;
  z-index: 2;
  -webkit-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  -moz-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  -o-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
}
.page-container .sidebar-menu #main-menu li a i {
  top: 1px;
  display: inline-block;
}
.page-container .sidebar-menu #main-menu li a span {
  -webkit-transition: 350ms opacity ease-in-out;
  -moz-transition: 350ms opacity ease-in-out;
  -o-transition: 350ms opacity ease-in-out;
  transition: 350ms opacity ease-in-out;
}
.page-container .sidebar-menu #main-menu li a:hover {
  background-color: rgba(69, 74, 84, 0.3);
  color: #fff;
}
.page-container .sidebar-menu #main-menu li a .badge {
  position: relative;
  float: right;
  font-size: 11px;
  line-height: 1.3;
}
.page-container .sidebar-menu #main-menu li a .badge.badge-primary {
  border: 1px solid #454a54;
  top: -1px;
}
.page-container .sidebar-menu #main-menu li.active > a {
  background-color: #2b303a;
  color: #fff;
}
.page-container .sidebar-menu #main-menu li.has-sub > a:before {
  position: relative;
  content: '\e877';
  content: '\e879';
  display: inline-block;
  font-family: "Entypo";
  color: #454a54;
  color: #FFF;
  float: right;
  font-size: 15px;
  margin-left: 13px;
  top: 0px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container .sidebar-menu #main-menu li.has-sub.opened > a:before {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
.page-container .sidebar-menu #main-menu li ul {
  position: relative;
  list-style: none;
  padding: 0;
  margin: 0;
  border-top: 1px solid rgba(69, 74, 84, 0.4);
  display: none;
  overflow: hidden;
  z-index: 1;
}
.page-container .sidebar-menu #main-menu li ul.visible {
  display: block;
}
.page-container .sidebar-menu #main-menu li ul > li {
  border-bottom: 1px solid rgba(69, 74, 84, 0.4);
}
.page-container .sidebar-menu #main-menu li ul > li > a {
  background-color: #2b303a;
  padding-left: 40px;
}
.page-container .sidebar-menu #main-menu li ul > li > a:hover {
  background-color: #2d323d;
}
.page-container .sidebar-menu #main-menu li ul > li:last-child {
  border-bottom: 0;
}
.page-container .sidebar-menu #main-menu li ul > li ul > li > a {
  padding-left: 60px;
  background: #262b34;
}
.page-container .sidebar-menu #main-menu li ul > li ul > li ul > li > a {
  padding-left: 80px;
  background: #262b34;
}
.page-container .sidebar-menu #main-menu li ul > li ul > li ul > li ul > li > a {
  padding-left: 100px;
  background: #262b34;
}
.page-container .sidebar-menu .sidebar-user-info {
  position: relative;
  padding: 25px 35px;
  border-top: 1px solid #454a54;
  -webkit-transition: padding 300ms ease-in-out;
  -moz-transition: padding 300ms ease-in-out;
  -o-transition: padding 300ms ease-in-out;
  transition: padding 300ms ease-in-out;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.page-container .sidebar-menu .sidebar-user-info .user-link {
  display: block;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.page-container .sidebar-menu .sidebar-user-info .user-link:before,
.page-container .sidebar-menu .sidebar-user-info .user-link:after {
  content: " ";
  display: table;
}
.page-container .sidebar-menu .sidebar-user-info .user-link:after {
  clear: both;
}
.page-container .sidebar-menu .sidebar-user-info .user-link img {
  float: left;
  margin-right: 15px;
  border: 4px solid rgba(255, 255, 255, 0.11);
  -webkit-transition: transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -o-transition: transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950);
  transition: transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -webkit-transition: -webkit-transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -moz-transition: -moz-transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -o-transition: -o-transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950);
  transition: -webkit-transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950),-moz-transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950),-o-transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950),transform 350ms cubic-bezier(0.445, 0.050, 0.550, 0.950);
  display: block;
  max-width: 100%;
  height: auto;
}
.page-container .sidebar-menu .sidebar-user-info .user-link span,
.page-container .sidebar-menu .sidebar-user-info .user-link strong {
  display: block;
  font-size: 14px;
  color: #fff;
  font-weight: normal;
  line-height: 1.2;
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -webkit-transition: opacity 300ms ease-in-out;
  -moz-transition: opacity 300ms ease-in-out;
  -o-transition: opacity 300ms ease-in-out;
  transition: opacity 300ms ease-in-out;
}
.page-container .sidebar-menu .sidebar-user-info .user-link span {
  zoom: 1;
  filter: alpha(opacity=80);
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
}
.page-container .sidebar-menu .sidebar-user-info .user-link strong {
  font-size: 18px;
}
.page-container .sidebar-menu .sidebar-user-info .user-link img + span,
.page-container .sidebar-menu .sidebar-user-info .user-link img + strong {
  padding-top: 8px;
}
.page-container .sidebar-menu .sidebar-user-info .sui-normal {
  position: relative;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover {
  position: absolute;
  visibility: hidden;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: #2b303a;
  padding: 25px 27.5px;
  -webkit-transition: opacity 200ms ease-in-out;
  -moz-transition: opacity 200ms ease-in-out;
  -o-transition: opacity 200ms ease-in-out;
  transition: opacity 200ms ease-in-out;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover .close-sui-popup {
  position: absolute;
  right: 15px;
  top: 10px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  cursor: pointer;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover a {
  position: relative;
  color: #fff;
  display: block;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover a + a {
  margin-left: 0;
  margin-top: 5px;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a {
  top: -20px;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: all 350ms cubic-bezier(0.680, -0.550, 0.265, 1.550);
  -moz-transition: all 350ms cubic-bezier(0.680, -0.550, 0.265, 1.550);
  -o-transition: all 350ms cubic-bezier(0.680, -0.550, 0.265, 1.550);
  transition: all 350ms cubic-bezier(0.680, -0.550, 0.265, 1.550);
  -webkit-transition-delay: 90ms;
  -moz-transition-delay: 90ms;
  -o-transition-delay: 90ms;
  transition-delay: 90ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a {
  -webkit-transition-delay: 180ms;
  -moz-transition-delay: 180ms;
  -o-transition-delay: 180ms;
  transition-delay: 180ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a {
  -webkit-transition-delay: 270ms;
  -moz-transition-delay: 270ms;
  -o-transition-delay: 270ms;
  transition-delay: 270ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a + a {
  -webkit-transition-delay: 360ms;
  -moz-transition-delay: 360ms;
  -o-transition-delay: 360ms;
  transition-delay: 360ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a + a + a {
  -webkit-transition-delay: 450ms;
  -moz-transition-delay: 450ms;
  -o-transition-delay: 450ms;
  transition-delay: 450ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a + a + a + a {
  -webkit-transition-delay: 540ms;
  -moz-transition-delay: 540ms;
  -o-transition-delay: 540ms;
  transition-delay: 540ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a + a + a + a + a {
  -webkit-transition-delay: 630ms;
  -moz-transition-delay: 630ms;
  -o-transition-delay: 630ms;
  transition-delay: 630ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a + a + a + a + a + a {
  -webkit-transition-delay: 720ms;
  -moz-transition-delay: 720ms;
  -o-transition-delay: 720ms;
  transition-delay: 720ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a + a + a + a + a + a + a {
  -webkit-transition-delay: 810ms;
  -moz-transition-delay: 810ms;
  -o-transition-delay: 810ms;
  transition-delay: 810ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a + a + a + a + a + a + a + a {
  -webkit-transition-delay: 900ms;
  -moz-transition-delay: 900ms;
  -o-transition-delay: 900ms;
  transition-delay: 900ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.animate-in a + a + a + a + a + a + a + a + a + a + a {
  -webkit-transition-delay: 990ms;
  -moz-transition-delay: 990ms;
  -o-transition-delay: 990ms;
  transition-delay: 990ms;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.visible {
  visibility: visible;
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.visible .close-sui-popup {
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.visible.animate-in a {
  top: 0;
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.going-invisible {
  visibility: visible;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.inline-links a {
  line-height: 52px;
  color: #fff;
  display: inline-block;
}
.page-container .sidebar-menu .sidebar-user-info .sui-hover.inline-links a + a {
  margin-left: 10px;
}
.page-container .main-content {
  position: relative;
  /*
float: left;
		z-index: 2;
*/
  display: table-cell;
  vertical-align: top;
  padding: 20px;
  background: transparent;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
@media screen and (max-width: 767px) {
  .page-container .main-content {
    display: block;
  }
}
.page-container.sidebar-collapsed .sidebar-menu {
  width: 65px;
}
.page-container.sidebar-collapsed .sidebar-menu .sidebar-menu-inner {
  width: 65px;
}
.page-container.sidebar-collapsed .sidebar-menu.fixed .sidebar-menu-inner {
  position: relative;
}
.page-container.sidebar-collapsed .sidebar-user-info {
  padding: 13.63636364px 5px;
}
.page-container.sidebar-collapsed .sidebar-user-info .user-link {
  text-align: center;
}
.page-container.sidebar-collapsed .sidebar-user-info .user-link img {
  -webkit-transform: scale(0.8);
  -moz-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
}
.page-container.sidebar-collapsed .sidebar-user-info .user-link span,
.page-container.sidebar-collapsed .sidebar-user-info .user-link strong {
  position: absolute;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.page-container.sidebar-collapsed .sidebar-user-info .sui-hover {
  position: absolute;
  top: 0;
  left: 65px;
  width: 280px;
  padding-top: 8px;
  padding-bottom: 8px;
  background: #303641;
  border: 1px solid #454a54;
  border-left: 0;
}
.page-container.sidebar-collapsed .sidebar-user-info .sui-hover a {
  line-height: 1.2;
}
.page-container.sidebar-collapsed.sidebar-is-showing .sidebar-user-info {
  padding: 25px 35px;
}
.page-container.sidebar-collapsed.sidebar-is-showing .sidebar-user-info img {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}
.page-container.sidebar-collapsed .sidebar-menu {
  z-index: 100;
  width: 65px;
}
.page-container.sidebar-collapsed .sidebar-menu .logo-env {
  display: block;
  padding: 20px;
}
.page-container.sidebar-collapsed .sidebar-menu .logo-env > div.logo {
  overflow: hidden;
  width: 0;
}
.page-container.sidebar-collapsed .sidebar-menu .logo-env > div.sidebar-collapse {
  display: block;
  padding: 0;
  left: 3px;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li#search .search-input {
  position: absolute;
  visibility: hidden;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li#search button {
  padding-left: 20px;
  padding-bottom: 20px;
  margin: 0;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li#search.focused .search-input {
  display: block;
  position: absolute;
  visibility: visible;
  top: 0px;
  left: 65px;
  background: #262b34;
  border: 1px solid #454a54;
  width: 250px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
  border-left: 0;
  padding: 15px 20px;
  padding-bottom: 16px;
  border: 0;
  padding-left: 0;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub > a:before {
  display: none;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub > a {
  background-color: transparent;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > a {
  position: relative;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > a > span:not(.badge) {
  display: block;
  position: absolute;
  top: 0;
  left: 65px;
  margin-top: -1px;
  padding: 12px 10px;
  background: #303641;
  border-top: 1px solid #454a54;
  border-bottom: 1px solid #454a54;
  width: 250px;
  visibility: hidden;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > a span {
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > a span.badge {
  position: absolute;
  right: -240px;
  top: 10px;
  display: none;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li ul {
  border-top-color: #454a54;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul {
  display: block;
  visibility: hidden;
  position: absolute;
  height: 0;
  top: auto;
  left: 65px;
  width: 250px;
  -webkit-border-radius: 0 0 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 0;
  background-clip: padding-box;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul > li > a span.badge {
  display: none;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul li {
  border-bottom: 1px solid #454a54;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul li a {
  padding-left: 10px;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul li li a {
  padding-left: 25px;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul li li li a {
  padding-left: 40px;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li > ul li li li li a {
  padding-left: 55px;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover.has-sub > a,
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover.has-sub > a {
  cursor: default;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover.has-sub > a > span:not(.badge),
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover.has-sub > a > span:not(.badge) {
  -webkit-border-radius: 0 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 0 0;
  background-clip: padding-box;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover.has-sub > a:hover,
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover.has-sub > a:hover {
  color: #aaabae;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover > ul,
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover > ul {
  height: auto;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover > a > span,
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover > a > span {
  visibility: visible;
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover a span.badge,
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover a span.badge {
  display: block;
}
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover > ul,
.page-container.sidebar-collapsed .sidebar-menu #main-menu > li:hover > ul {
  visibility: visible;
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.page-container.sidebar-is-collapsing .sidebar-user-info {
  padding: 13.63636364px 5px;
}
.page-container.sidebar-is-collapsing .sidebar-user-info img {
  -webkit-transform: scale(0.8);
  -moz-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
}
.page-container.sidebar-is-collapsing .sidebar-menu #main-menu li#search .search-input {
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container.sidebar-is-collapsing .sidebar-menu #main-menu li a:before {
  display: none;
}
.page-container.sidebar-is-collapsing .sidebar-menu #main-menu li a span {
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: opacity 250ms ease-in-out !important;
  -moz-transition: opacity 250ms ease-in-out !important;
  -o-transition: opacity 250ms ease-in-out !important;
  transition: opacity 250ms ease-in-out !important;
}
.page-container.sidebar-is-showing .sidebar-menu #main-menu li#search .search-input {
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.page-container.sidebar-is-showing .sidebar-menu #main-menu li a:before {
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.page-container.sidebar-is-showing .sidebar-menu #main-menu li a span {
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.page-container.no-sidebar {
  padding-left: 0;
}
.page-container.no-sidebar .main-content {
  padding-left: 20px;
}
.page-container.horizontal-menu {
  padding-left: 0;
  padding-top: 62px;
}
.page-container.horizontal-menu .navbar {
  display: table-row;
}
.page-container.horizontal-menu.with-sidebar.fit-logo-with-sidebar header.navbar .navbar-brand {
  width: 280px;
}
.page-container.horizontal-menu.with-sidebar header.navbar {
  border-bottom: 1px solid #454a54;
}
.page-container.horizontal-menu.with-sidebar header.navbar ul.nav li.dropdown.open:after {
  bottom: -1px;
}
.page-container.horizontal-menu.with-sidebar header.navbar ul.nav li.dropdown.open .dropdown-menu {
  margin-top: 1px;
}
.page-container.horizontal-menu.with-sidebar .sidebar-user-info {
  border-top: 0;
  border-bottom: 1px solid #454a54;
}
.page-container.horizontal-menu.with-sidebar .sidebar-menu {
  position: relative;
  z-index: 100;
}
.page-container.horizontal-menu.with-sidebar .main-content {
  width: 100%;
}
.page-container.horizontal-menu header.navbar {
  position: absolute;
  z-index: 8;
  background-color: #303641;
  padding: 0;
  margin-bottom: 0;
  border: 0;
  left: 0;
  right: 0;
  top: 0;
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
}
.page-container.horizontal-menu header.navbar:before,
.page-container.horizontal-menu header.navbar:after {
  content: " ";
  display: table;
}
.page-container.horizontal-menu header.navbar:after {
  clear: both;
}
@media screen and (max-width: 768px) {
  .page-container.horizontal-menu header.navbar {
    display: block;
    min-height: 63px;
  }
}
.page-container.horizontal-menu header.navbar.navbar-fixed-top {
  position: fixed;
  z-index: 10000;
}
@media screen and (max-width: 768px) {
  .page-container.horizontal-menu header.navbar.navbar-fixed-top {
    z-index: 10;
  }
}
.page-container.horizontal-menu header.navbar > .navbar-inner:before,
.page-container.horizontal-menu header.navbar > .navbar-inner:after {
  content: " ";
  display: table;
}
.page-container.horizontal-menu header.navbar > .navbar-inner:after {
  clear: both;
}
.page-container.horizontal-menu header.navbar .navbar-brand {
  padding: 20px 20px;
}
.page-container.horizontal-menu header.navbar .navbar-nav {
  border-left: 1px solid rgba(69, 74, 84, 0.5);
  margin: 0;
  padding: 0;
  list-style: none;
}
.page-container.horizontal-menu header.navbar .navbar-nav:before,
.page-container.horizontal-menu header.navbar .navbar-nav:after {
  content: " ";
  display: table;
}
.page-container.horizontal-menu header.navbar .navbar-nav:after {
  clear: both;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li {
  position: relative;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li > a {
  position: relative;
  display: block;
  padding: 20px 20px;
  border-right: 1px solid rgba(69, 74, 84, 0.5);
  font-size: 12px;
  color: #aaabae;
  -webkit-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  -moz-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  -o-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  z-index: 10;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li > a i {
  top: 1px;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li > a span {
  font-size: 12px;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li:hover > a {
  background-color: rgba(69, 74, 84, 0.3);
  color: #fff;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li#search {
  border-right: 1px solid rgba(69, 74, 84, 0.5);
}
.page-container.horizontal-menu header.navbar .navbar-nav > li#search.search-input-collapsed:hover {
  background-color: rgba(69, 74, 84, 0.3);
}
.page-container.horizontal-menu header.navbar .navbar-nav > li.active > a {
  background-color: #2b303a;
  color: #fff;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li.active.has-sub > a:after {
  position: absolute;
  content: '';
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 6px 6px 0 6px;
  border-color: #2b303a transparent transparent transparent;
  bottom: -6px;
  left: 50%;
  margin-left: -6px;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
  -webkit-transition-delay: 300ms;
  -moz-transition-delay: 300ms;
  -o-transition-delay: 300ms;
  transition-delay: 300ms;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li.active.has-sub:hover > a:after {
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li .badge {
  margin-left: 10px;
  margin-right: -5px;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul {
  position: absolute;
  list-style: none;
  margin: 0;
  padding: 0;
  left: 0;
  top: auto;
  margin-top: 1px;
  background-color: #272b34;
  min-width: 250px;
  display: none;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul li {
  position: relative;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul li a {
  display: block;
  padding: 10px 20px;
  white-space: nowrap;
  font-size: 12px;
  border-bottom: 1px solid #303641;
  color: #aaabae;
  -webkit-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  -moz-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  -o-transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
  transition: color 250ms ease-in-out, background-color 250ms ease-in-out;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul li.active > a {
  background-color: #2b303a;
  color: #fff;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul li:hover > a {
  background-color: #2b303a;
  color: #fff;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul li ul {
  position: absolute;
  left: 100%;
  top: -1px;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul li.has-sub > a:before {
  position: relative;
  content: '\e877';
  content: '\e879';
  display: inline-block;
  font-family: "Entypo";
  color: #aaabae;
  color: #FFF;
  float: right;
  font-size: 15px;
  margin-left: 20px;
  top: -1px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul li.has-sub:hover > a:before {
  color: #fff;
}
.page-container.horizontal-menu header.navbar .navbar-nav > li ul > li:last-child > a {
  border-bottom: 0;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search,
.page-container.horizontal-menu header.navbar > ul > li#search {
  position: relative;
  background-color: #262b34;
  -webkit-transition: background 300ms ease-in-out;
  -moz-transition: background 300ms ease-in-out;
  -o-transition: background 300ms ease-in-out;
  transition: background 300ms ease-in-out;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search ::-webkit-input-placeholder,
.page-container.horizontal-menu header.navbar > ul > li#search ::-webkit-input-placeholder {
  color: rgba(170, 171, 174, 0.7);
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search :-moz-placeholder,
.page-container.horizontal-menu header.navbar > ul > li#search :-moz-placeholder {
  color: rgba(170, 171, 174, 0.7);
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search ::-moz-placeholder,
.page-container.horizontal-menu header.navbar > ul > li#search ::-moz-placeholder {
  color: rgba(170, 171, 174, 0.7);
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search :-ms-input-placeholder,
.page-container.horizontal-menu header.navbar > ul > li#search :-ms-input-placeholder {
  color: rgba(170, 171, 174, 0.7);
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input,
.page-container.horizontal-menu header.navbar > ul > li#search .search-input {
  background: none;
  padding: 0;
  margin: 0;
  border: none;
  outline: none;
  padding: 22px 20px;
  padding-right: 45px;
  width: 220px;
  color: #aaabae;
  font-size: 12px;
  line-height: 1.42857143;
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -webkit-transition: all 350 ease-in-out;
  -o-transition: all 350 ease-in-out;
  transition: all 350 ease-in-out;
  -webkit-transition: all 350ms ease-in-out;
  -moz-transition: all 350ms ease-in-out;
  -o-transition: all 350ms ease-in-out;
  transition: all 350ms ease-in-out;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search button,
.page-container.horizontal-menu header.navbar > ul > li#search button {
  position: absolute;
  right: 0;
  top: 0;
  background: none;
  padding: 0;
  margin: 0;
  border: none;
  outline: none;
  color: #aaabae;
  padding: 20px 10px;
  -webkit-transition: all 350 ease-in-out;
  -o-transition: all 350 ease-in-out;
  transition: all 350 ease-in-out;
  -webkit-transition: all 350ms ease-in-out;
  -moz-transition: all 350ms ease-in-out;
  -o-transition: all 350ms ease-in-out;
  transition: all 350ms ease-in-out;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search button i,
.page-container.horizontal-menu header.navbar > ul > li#search button i {
  margin: 0;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search.search-input-collapsed,
.page-container.horizontal-menu header.navbar > ul > li#search.search-input-collapsed {
  background-color: transparent;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search.search-input-collapsed ::-webkit-input-placeholder,
.page-container.horizontal-menu header.navbar > ul > li#search.search-input-collapsed ::-webkit-input-placeholder {
  color: transparent;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search.search-input-collapsed :-moz-placeholder,
.page-container.horizontal-menu header.navbar > ul > li#search.search-input-collapsed :-moz-placeholder {
  color: transparent;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search.search-input-collapsed ::-moz-placeholder,
.page-container.horizontal-menu header.navbar > ul > li#search.search-input-collapsed ::-moz-placeholder {
  color: transparent;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search.search-input-collapsed :-ms-input-placeholder,
.page-container.horizontal-menu header.navbar > ul > li#search.search-input-collapsed :-ms-input-placeholder {
  color: transparent;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search.search-input-collapsed .search-input,
.page-container.horizontal-menu header.navbar > ul > li#search.search-input-collapsed .search-input {
  overflow: hidden;
  width: 45px;
  padding-left: 0;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
.page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search.search-input-collapsed button,
.page-container.horizontal-menu header.navbar > ul > li#search.search-input-collapsed button {
  padding-right: 15px;
}
.page-container.horizontal-menu header.navbar ul.nav {
  color: #aaabae;
  font-size: 12px;
}
.page-container.horizontal-menu header.navbar ul.nav:before,
.page-container.horizontal-menu header.navbar ul.nav:after {
  content: " ";
  display: table;
}
.page-container.horizontal-menu header.navbar ul.nav:after {
  clear: both;
}
.page-container.horizontal-menu header.navbar ul.nav > li {
  float: left;
}
.page-container.horizontal-menu header.navbar ul.nav > li > a,
.page-container.horizontal-menu header.navbar ul.nav > li > span {
  display: block;
  float: left;
  padding: 22px 20px;
  color: #aaabae;
  font-size: 12px;
  line-height: 1.42857143;
}
.page-container.horizontal-menu header.navbar ul.nav > li > a i,
.page-container.horizontal-menu header.navbar ul.nav > li > span i {
  margin-right: 5px;
}
.page-container.horizontal-menu header.navbar ul.nav > li > a i.right,
.page-container.horizontal-menu header.navbar ul.nav > li > span i.right {
  margin-right: 0;
  margin-left: 5px;
}
.page-container.horizontal-menu header.navbar ul.nav > li > a {
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container.horizontal-menu header.navbar ul.nav > li > a:hover,
.page-container.horizontal-menu header.navbar ul.nav > li > a:focus {
  color: #fff;
  background-color: transparent;
}
.page-container.horizontal-menu header.navbar ul.nav > li.sep {
  float: left;
  display: block;
  border-left: 1px solid #454a54;
  margin: 22px 0;
  height: 16px;
  width: 0px;
  line-height: 1.42857143;
  overflow: hidden;
}
.page-container.horizontal-menu header.navbar ul.nav > li#search button {
  padding: 22px 20px;
}
.page-container.horizontal-menu header.navbar ul.nav > li#search.search-input-collapsed button {
  padding-right: 15px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown > a {
  position: relative;
  display: block;
  padding: 0;
  margin: 10px;
  margin-top: 18px;
  margin-bottom: 16px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown > a i {
  font-size: 19px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown > a .badge {
  position: absolute;
  padding: 2px;
  font-size: 10px;
  right: 0px;
  top: -5px;
  min-width: 17px;
  line-height: 12px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu {
  background-color: #f5f5f6;
  -webkit-border-radius: 0 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 3px;
  background-clip: padding-box;
  -webkit-box-shadow: 0 1px 0px rgba(0,0,0,.05);
  -moz-box-shadow: 0 1px 0px rgba(0,0,0,.05);
  box-shadow: 0 1px 0px rgba(0,0,0,.05);
  border: 1px solid #f8f8f8;
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: 0px;
  width: 370px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu li,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu a,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu p {
  color: #888888;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li {
  display: block;
  border-bottom: 1px solid #ededed;
  margin: 0;
  padding: 0;
  font-size: 12px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li.top > p {
  background-color: #f5f5f6;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > a,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > p {
  margin: 0;
  padding: 12px 20px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > p {
  background-color: #FFF;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > p a {
  color: #ec5956;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > p a:hover {
  color: #e9423f;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li .scroller {
  max-height: 290px;
  overflow: auto;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul {
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li {
  font-size: 12px;
  border-bottom: 1px solid #ededed;
  background: #f5f5f6;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > a,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > p {
  display: block;
  position: relative;
  margin: 0;
  padding: 10px 20px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > a:before,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > p:before,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > a:after,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > p:after {
  content: " ";
  display: table;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > a:after,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > p:after {
  clear: both;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > a > i,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > p > i {
  background: #454a54;
  display: block;
  float: left;
  text-align: center;
  padding: 5px;
  line-height: 14px;
  color: #fff;
  width: 28px;
  height: 24px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > a:hover,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li > p:hover {
  background-color: #f5f5f6 !important;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-primary > i,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-primary > a > i {
  background-color: #303641;
  color: #fff;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-secondary > i,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-secondary > a > i {
  background-color: #ee4749;
  color: #fff;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-success > i,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-success > a > i {
  background-color: #00a651;
  color: #fff;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-info > i,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-info > a > i {
  background-color: #21a9e1;
  color: #fff;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-warning > i,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-warning > a > i {
  background-color: #fad839;
  color: #fff;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-danger > i,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li.notification-danger > a > i {
  background-color: #cc2424;
  color: #fff;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .task {
  display: block;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .task:before,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .task:after {
  content: " ";
  display: table;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .task:after {
  clear: both;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .task span {
  display: block;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .task span.desc {
  float: left;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .task span.percent {
  float: right;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .image {
  display: block;
  margin-right: 10px;
  float: left;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .image.pull-right {
  margin-left: 10px;
  margin-right: 0;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .image .img-circle {
  border: 2px solid #FFF;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .image + .line {
  margin-top: 8px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .line {
  display: block;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .line.desc {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .line + .line,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .line + .line + .line,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .line + .line + .line + .line {
  margin-top: 5px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li strong {
  color: #373e4a;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .progress {
  display: block;
  height: 5px;
  background-color: #ededed;
  margin: 0;
  margin-top: 10px;
  margin-bottom: 5px;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .progress:before,
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .progress:after {
  content: " ";
  display: table;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li .progress:after {
  clear: both;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li:last-child {
  border-bottom: 0;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li > ul > li:first-child a:afterx {
  position: absolute;
  content: '';
  display: block;
  height: 10px;
  top: -10px;
  left: 0;
  right: 0;
  background: transparent;
  background: red;
  -webkit-box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.03);
  -moz-box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.03);
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.03);
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li.external {
  background-color: #FFF;
  border-bottom: 0;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li.external a {
  color: #ec5956;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu > li.external a:hover {
  background-color: #fff;
  color: #e9423f;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown.open {
  background-color: rgba(69, 74, 84, 0.5);
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown.open > a {
  background-color: transparent;
  color: #fff;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown.open:after {
  content: '';
  position: absolute;
  display: block;
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 0 7px 7px 7px;
  border-color: transparent transparent #FFF transparent;
  bottom: -2px;
  left: 50%;
  margin-left: -7px;
}
.page-container.horizontal-menu header.navbar ul.nav > li .horizontal-mobile-menu {
  position: relative;
}
.page-container.horizontal-menu header.navbar ul.nav > li .horizontal-mobile-menu a {
  display: inline-block;
  border: 1px solid #454a54;
  text-align: center;
  padding: 0;
  line-height: 1;
  font-size: 20px;
  font-weight: 300;
  padding: 5px 2px;
  color: #aaabae;
  margin-top: 15px;
  margin-left: 20px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 200ms ease-in-out;
  -moz-transition: all 200ms ease-in-out;
  -o-transition: all 200ms ease-in-out;
  transition: all 200ms ease-in-out;
}
.page-container.horizontal-menu header.navbar ul.nav > li .horizontal-mobile-menu a:hover {
  background-color: rgba(69, 74, 84, 0.4);
}
/* Lower screen than large screen */
@media (max-width: 992px) {
  .page-body .page-container.horizontal-menu.fit-logo-with-sidebar header.navbar .navbar-brand {
    width: auto;
  }
}
@media (min-width: 767px) and (max-width: 1100px) {
  .page-container.horizontal-menu header.navbar .navbar-nav > li > a {
    padding-left: 10px;
    padding-right: 10px;
    -webkit-transition: padding 250ms ease-in-out;
    -moz-transition: padding 250ms ease-in-out;
    -o-transition: padding 250ms ease-in-out;
    transition: padding 250ms ease-in-out;
  }
  .page-container.horizontal-menu header.navbar .navbar-nav > li > a .badge {
    display: none;
  }
  .page-container.horizontal-menu header.navbar > ul > li#search .search-input,
  .page-container.horizontal-menu header.navbar .navbar-inner > ul > li#search .search-input {
    width: 140px;
  }
  .page-container.horizontal-menu header.navbar .navbar-nav > li ul li a {
    padding: 10px 15px;
  }
}
/* Tablets Only */
@media (min-width: 767px) and (max-width: 992px) {
  .page-container.horizontal-menu header.navbar .navbar-nav > li > a {
    padding-left: 15px;
    padding-right: 15px;
  }
  .page-container.horizontal-menu header.navbar .navbar-nav > li > a i + span {
    display: none;
  }
  .page-container.Xhorizontal-menu header.navbar .navbar-nav li a,
  .page-container.Xhorizontal-menu header.navbar .navbar-nav li ul li a,
  .page-container.Xhorizontal-menu header.navbar ul.nav > li > a {
    padding-left: 10px;
    padding-right: 10px;
    font-size: 11px;
  }
  .page-container.Xhorizontal-menu header.navbar ul > li#search .search-input {
    width: 150px;
  }
  .page-container.Xhorizontal-menu header.navbar ul.nav > li.dropdown .dropdown-menu {
    width: 315px;
  }
}
/* Relatively-Small screen */
@media (max-width: 767px) {
  .page-body .page-container {
    padding-left: 0;
  }
  .page-body .page-container .sidebar-menu {
    position: relative;
    width: 100%;
    min-height: 0px !important;
  }
  .page-body .page-container .sidebar-menu .logo-env {
    padding: 15px 20px;
    -webkit-transition: all padding ease-in-out;
    -moz-transition: all padding ease-in-out;
    -o-transition: all padding ease-in-out;
    transition: all padding ease-in-out;
  }
  .page-body .page-container .sidebar-menu .logo-env > div.logo {
    display: block;
    width: auto;
  }
  .page-body .page-container .sidebar-menu .logo-env > div.sidebar-collapse {
    display: none;
  }
  .page-body .page-container .sidebar-menu #main-menu {
    display: none;
    margin-bottom: 0;
  }
  .page-body .page-container .sidebar-menu #main-menu li a span {
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
  }
  .page-body .page-container .main-content {
    min-height: auto !important;
  }
  .page-body .page-container.horizontal-menu {
    padding-top: 0;
  }
  .page-body .page-container.horizontal-menu.with-sidebar {
    padding-left: 0;
  }
  .page-body .page-container.horizontal-menu header.navbar {
    position: relative;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav {
    clear: left;
    border-top: 1px solid rgba(69, 74, 84, 0.4);
    display: none;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li > a {
    border-right: 0;
    border-bottom: 1px solid rgba(69, 74, 84, 0.4);
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li#search {
    border-right: 0;
    background-color: #262b34;
    border-bottom: 1px solid rgba(69, 74, 84, 0.4);
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li#search ::-webkit-input-placeholder {
    color: #aaabae;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li#search :-moz-placeholder {
    color: #aaabae;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li#search ::-moz-placeholder {
    color: #aaabae;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li#search :-ms-input-placeholder {
    color: #aaabae;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li#search:hover {
    background-color: #262b34;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li#search .search-input {
    zoom: 1;
    filter: alpha(opacity=100);
    -webkit-opacity: 1;
    -moz-opacity: 1;
    opacity: 1;
    width: 100%;
    padding-left: 20px;
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li#search button {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li > ul > li > a:first-child {
    border-top: 0;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul {
    overflow: hidden;
    width: 100%;
    position: relative;
    left: 0;
    -webkit-transform: none !important;
    -moz-transform: none !important;
    -ms-transform: none !important;
    -o-transform: none !important;
    transform: none !important;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul.visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li a {
    border: none;
    border-bottom: 1px solid rgba(69, 74, 84, 0.3);
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li:hover > a {
    background: transparent;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li > a:hover {
    background-color: rgba(69, 74, 84, 0.2);
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li.active > a {
    background-color: rgba(69, 74, 84, 0.15);
    color: #fff;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li.opened > a:before {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li a {
    padding-left: 40px;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li li a {
    padding-left: 60px;
  }
  .page-body .page-container.horizontal-menu header.navbar .navbar-nav > li ul li li li a {
    padding-left: 80px;
  }
  .page-body .page-container.horizontal-menu header.navbar ul.nav {
    position: absolute;
    top: 0px;
    right: 20px;
  }
  .page-body .page-container.horizontal-menu header.navbar ul.nav > li {
    display: none;
  }
  .page-body .page-container.horizontal-menu header.navbar ul.nav > li.dropdown {
    display: block;
  }
  .page-body .page-container.horizontal-menu header.navbar ul.nav > li.dropdown.open > .dropdown-menu {
    width: 259px;
    margin-left: -185px;
    -webkit-border-radius: 3px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px;
    -moz-background-clip: padding;
    border-radius: 3px;
    background-clip: padding-box;
  }
  .page-body .page-container .main-content {
    min-height: 0 !important;
  }
}
/* Small Screen */
@media (max-width: 480px) {
  .page-body .page-container.horizontal-menu header.navbar .horizontal-mobile-menu a {
    margin-left: 10px;
  }
  .page-body .page-container.horizontal-menu header.navbar ul.nav > li.dropdown > a {
    margin-left: 5px;
    margin-right: 5px;
  }
  .page-body .page-container.horizontal-menu header.navbar ul.nav > li.dropdown.open > .dropdown-menu {
    width: 222px;
    margin-left: -123.33333333px;
  }
}
/* Right Sidebar Menu */
.page-container.right-sidebar .sidebar-menu .sidebar-user-info .sui-hover.inline-links a {
  margin-left: 10px;
}
.page-container.right-sidebar .sidebar-menu .sidebar-user-info .sui-hover .close-sui-popup {
  right: auto;
  left: 15px;
}
@media screen and (min-width: 991px) {
  .page-container.right-sidebar {
    direction: rtl;
  }
  .page-container.right-sidebar > * {
    direction: ltr;
  }
  .page-container.right-sidebar .sidebar-menu {
    left: auto;
    right: 0;
  }
  .page-container.right-sidebar.sidebar-collapsed {
    padding-left: 0;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > a > span,
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > ul {
    left: auto;
    right: 65px;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > a > i {
    margin-right: 0;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > a > span.badge {
    right: 75px;
    left: auto;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > a > span:not(.badge) {
    -webkit-border-radius: 3px 0 0 3px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px 0 0 3px;
    -moz-background-clip: padding;
    border-radius: 3px 0 0 3px;
    background-clip: padding-box;
    padding-left: 15px;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > ul li a {
    padding-left: 15px;
    padding-right: 10px;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > ul li a i {
    margin-right: 0;
    margin-left: 5px;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > ul {
    right: 65px;
    left: auto;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li > ul {
    -webkit-border-radius: 0 0 0 3px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 0 0 0 3px;
    -moz-background-clip: padding;
    border-radius: 0 0 0 3px;
    background-clip: padding-box;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li#search.focused .search-input {
    left: auto;
    right: 65px;
    -webkit-border-radius: 3px 0 0 3px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px 0 0 3px;
    -moz-background-clip: padding;
    border-radius: 3px 0 0 3px;
    background-clip: padding-box;
  }
  .page-container.right-sidebar.horizontal-menu.with-sidebar {
    padding-left: 0;
  }
  .page-container.right-sidebar.horizontal-menu.with-sidebar.sidebar-collapsed {
    padding-left: 0;
  }
  .page-container.right-sidebar.chat-visible .main-content {
    padding-left: 300px;
    padding-right: 20px;
  }
  .page-container.right-sidebar #chat {
    left: 0;
    right: auto;
  }
  .page-container.right-sidebar #chat .chat-conversation {
    left: 280px;
    right: auto;
    -webkit-border-radius: 0 3px 3px 0;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 0 3px 3px 0;
    -moz-background-clip: padding;
    border-radius: 0 3px 3px 0;
    background-clip: padding-box;
  }
  .page-container.right-sidebar.sidebar-is-collapsing .sidebar-menu #main-menu li a i {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }
  .page-container.right-sidebar.sidebar-is-collapsing .sidebar-menu {
    -webkit-transition: all 350ms;
    -moz-transition: all 350ms;
    -o-transition: all 350ms;
    transition: all 350ms;
  }
  .page-container.right-sidebar.sidebar-is-collapsing .sidebar-menu .sidebar-user-info .user-link span,
  .page-container.right-sidebar.sidebar-is-collapsing .sidebar-menu .sidebar-user-info .user-link strong {
    zoom: 1;
    filter: alpha(opacity=0);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    opacity: 0;
  }
  .page-container.right-sidebar.sidebar-is-collapsing.sidebar-collapsing-phase-2 {
    padding-right: 65px;
    -webkit-transition: all 600ms;
    -moz-transition: all 600ms;
    -o-transition: all 600ms;
    transition: all 600ms;
  }
  .page-container.right-sidebar.sidebar-is-collapsing.sidebar-collapsing-phase-2 .sidebar-menu {
    width: 65px;
  }
  .page-container.right-sidebar.sidebar-is-collapsing.sidebar-collapsing-phase-2 .sidebar-menu .logo-env > .logo,
  .page-container.right-sidebar.sidebar-is-collapsing.sidebar-collapsing-phase-2 .sidebar-menu #main-menu li > a:before,
  .page-container.right-sidebar.sidebar-is-collapsing.sidebar-collapsing-phase-2 .sidebar-menu #main-menu li a span,
  .page-container.right-sidebar.sidebar-is-collapsing.sidebar-collapsing-phase-2 .sidebar-menu .sidebar-user-info .user-link span,
  .page-container.right-sidebar.sidebar-is-collapsing.sidebar-collapsing-phase-2 .sidebar-menu .sidebar-user-info .user-link strong {
    zoom: 1;
    filter: alpha(opacity=0);
    -webkit-opacity: 0;
    -moz-opacity: 0;
    opacity: 0;
    visibility: hidden;
    display: none;
  }
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li.has-sub:hover.has-sub > a > span:not(.badge),
  .page-container.right-sidebar.sidebar-collapsed .sidebar-menu #main-menu > li:hover.has-sub > a > span:not(.badge) {
    -webkit-border-radius: 3px 0 0 0;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px 0 0 0;
    -moz-background-clip: padding;
    border-radius: 3px 0 0 0;
    background-clip: padding-box;
  }
}
.user-info {
  list-style: none;
  margin: 0;
  padding: 0;
}
.user-info:before,
.user-info:after {
  content: " ";
  display: table;
}
.user-info:after {
  clear: both;
}
.user-info > li {
  float: left;
  margin-right: 2.5px;
}
.user-info > li.profile-info {
  margin-right: 10px;
}
.profile-info.dropdown > a {
  color: #2b303a;
  text-decoration: none;
  font-size: 14px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-info.dropdown > a:hover {
  color: #818da2;
}
.profile-info.dropdown > a img {
  border: 2px solid #f5f5f5;
  margin-right: 5px;
}
.profile-info.dropdown .dropdown-menu {
  background-color: #303641;
  border-color: #454a54;
  padding: 0;
  margin-top: 15px;
}
.profile-info.dropdown .dropdown-menu > .caret {
  position: absolute;
  left: 20px;
  top: -4px;
  border-bottom-width: 4px;
  border-top-width: 0;
  border-bottom-color: #454a54;
  margin-left: -2px;
}
.profile-info.dropdown .dropdown-menu li {
  color: #aaabae;
  font-size: 12px;
  padding: 0;
}
.profile-info.dropdown .dropdown-menu li a {
  color: #aaabae;
  padding: 8px 10px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.profile-info.dropdown .dropdown-menu li a i {
  margin-right: 5px;
  zoom: 1;
  filter: alpha(opacity=70);
  -webkit-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
}
.profile-info.dropdown .dropdown-menu li a:hover {
  background-color: #2b303a;
  color: #fff;
}
.profile-info.dropdown .dropdown-menu li a:hover i {
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.profile-info.dropdown .dropdown-menu > li {
  border-bottom: 1px solid #454a54;
}
.profile-info.dropdown .dropdown-menu > li > a {
  display: block;
  min-width: 210px;
}
.profile-info.dropdown .dropdown-menu > li:last-child {
  border-bottom: 0;
}
.profile-info.dropdown.pull-right {
  text-align: right;
}
.profile-info.dropdown.pull-right > a img {
  margin-left: 5px;
  margin-right: 0;
}
.profile-info.dropdown.pull-right .dropdown-menu {
  background-color: #303641;
  border-color: #454a54;
  padding: 0;
  margin-top: 15px;
  text-align: left;
}
.profile-info.dropdown.pull-right .dropdown-menu > .caret {
  left: auto;
  right: 20px;
}
.notifications.dropdown > a {
  position: relative;
  display: block;
  padding: 10px 10px;
  color: #737881;
}
.notifications.dropdown > a i {
  font-size: 19px;
}
.notifications.dropdown > a .badge {
  position: absolute;
  padding: 2px;
  font-size: 10px;
  right: 6px;
  top: 6px;
  min-width: 17px;
  line-height: 12px;
}
.notifications.dropdown.open > a {
  background-color: #f5f5f6;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.notifications.dropdown .dropdown-menu {
  background-color: #f5f5f6;
  -webkit-border-radius: 0 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 3px;
  background-clip: padding-box;
  -webkit-box-shadow: 0 1px 0px rgba(0,0,0,.05);
  -moz-box-shadow: 0 1px 0px rgba(0,0,0,.05);
  box-shadow: 0 1px 0px rgba(0,0,0,.05);
  border: 1px solid #f8f8f8;
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: 0px;
  width: 370px;
}
.notifications.dropdown .dropdown-menu li,
.notifications.dropdown .dropdown-menu a,
.notifications.dropdown .dropdown-menu p {
  color: #888888;
}
.notifications.dropdown .dropdown-menu > li {
  display: block;
  border-bottom: 1px solid #ededed;
  margin: 0;
  padding: 0;
  font-size: 12px;
}
.notifications.dropdown .dropdown-menu > li.top > p {
  background-color: #f5f5f6;
}
.notifications.dropdown .dropdown-menu > li > a,
.notifications.dropdown .dropdown-menu > li > p {
  margin: 0;
  padding: 12px 20px;
}
.notifications.dropdown .dropdown-menu > li > p {
  background-color: #FFF;
}
.notifications.dropdown .dropdown-menu > li > p a {
  color: #ec5956;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.notifications.dropdown .dropdown-menu > li > p a:hover {
  color: #e9423f;
}
.notifications.dropdown .dropdown-menu > li .scroller {
  max-height: 290px;
  overflow: auto;
}
.notifications.dropdown .dropdown-menu > li > ul {
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
}
.notifications.dropdown .dropdown-menu > li > ul > li {
  font-size: 12px;
  border-bottom: 1px solid #ededed;
  background: #f5f5f6;
}
.notifications.dropdown .dropdown-menu > li > ul > li > a,
.notifications.dropdown .dropdown-menu > li > ul > li > p {
  display: block;
  position: relative;
  margin: 0;
  padding: 10px 20px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.notifications.dropdown .dropdown-menu > li > ul > li > a:before,
.notifications.dropdown .dropdown-menu > li > ul > li > p:before,
.notifications.dropdown .dropdown-menu > li > ul > li > a:after,
.notifications.dropdown .dropdown-menu > li > ul > li > p:after {
  content: " ";
  display: table;
}
.notifications.dropdown .dropdown-menu > li > ul > li > a:after,
.notifications.dropdown .dropdown-menu > li > ul > li > p:after {
  clear: both;
}
.notifications.dropdown .dropdown-menu > li > ul > li > a > i,
.notifications.dropdown .dropdown-menu > li > ul > li > p > i {
  background: #454a54;
  display: block;
  float: left;
  text-align: center;
  padding: 5px;
  line-height: 14px;
  color: #fff;
  width: 28px;
  height: 24px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.notifications.dropdown .dropdown-menu > li > ul > li > a:hover,
.notifications.dropdown .dropdown-menu > li > ul > li > p:hover {
  background-color: #f5f5f6 !important;
}
.notifications.dropdown .dropdown-menu > li > ul > li.notification-primary > i,
.notifications.dropdown .dropdown-menu > li > ul > li.notification-primary > a > i {
  background-color: #303641;
  color: #fff;
}
.notifications.dropdown .dropdown-menu > li > ul > li.notification-secondary > i,
.notifications.dropdown .dropdown-menu > li > ul > li.notification-secondary > a > i {
  background-color: #ee4749;
  color: #fff;
}
.notifications.dropdown .dropdown-menu > li > ul > li.notification-success > i,
.notifications.dropdown .dropdown-menu > li > ul > li.notification-success > a > i {
  background-color: #00a651;
  color: #fff;
}
.notifications.dropdown .dropdown-menu > li > ul > li.notification-info > i,
.notifications.dropdown .dropdown-menu > li > ul > li.notification-info > a > i {
  background-color: #21a9e1;
  color: #fff;
}
.notifications.dropdown .dropdown-menu > li > ul > li.notification-warning > i,
.notifications.dropdown .dropdown-menu > li > ul > li.notification-warning > a > i {
  background-color: #fad839;
  color: #fff;
}
.notifications.dropdown .dropdown-menu > li > ul > li.notification-danger > i,
.notifications.dropdown .dropdown-menu > li > ul > li.notification-danger > a > i {
  background-color: #cc2424;
  color: #fff;
}
.notifications.dropdown .dropdown-menu > li > ul > li .task {
  display: block;
}
.notifications.dropdown .dropdown-menu > li > ul > li .task:before,
.notifications.dropdown .dropdown-menu > li > ul > li .task:after {
  content: " ";
  display: table;
}
.notifications.dropdown .dropdown-menu > li > ul > li .task:after {
  clear: both;
}
.notifications.dropdown .dropdown-menu > li > ul > li .task span {
  display: block;
}
.notifications.dropdown .dropdown-menu > li > ul > li .task span.desc {
  float: left;
}
.notifications.dropdown .dropdown-menu > li > ul > li .task span.percent {
  float: right;
}
.notifications.dropdown .dropdown-menu > li > ul > li .image {
  display: block;
  margin-right: 10px;
  float: left;
}
.notifications.dropdown .dropdown-menu > li > ul > li .image.pull-right {
  margin-left: 10px;
  margin-right: 0;
}
.notifications.dropdown .dropdown-menu > li > ul > li .image .img-circle {
  border: 2px solid #FFF;
}
.notifications.dropdown .dropdown-menu > li > ul > li .image + .line {
  margin-top: 8px;
}
.notifications.dropdown .dropdown-menu > li > ul > li .line {
  display: block;
}
.notifications.dropdown .dropdown-menu > li > ul > li .line.desc {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.notifications.dropdown .dropdown-menu > li > ul > li .line + .line,
.notifications.dropdown .dropdown-menu > li > ul > li .line + .line + .line,
.notifications.dropdown .dropdown-menu > li > ul > li .line + .line + .line + .line {
  margin-top: 5px;
}
.notifications.dropdown .dropdown-menu > li > ul > li strong {
  color: #373e4a;
}
.notifications.dropdown .dropdown-menu > li > ul > li .progress {
  display: block;
  height: 5px;
  background-color: #ededed;
  margin: 0;
  margin-top: 10px;
  margin-bottom: 5px;
}
.notifications.dropdown .dropdown-menu > li > ul > li .progress:before,
.notifications.dropdown .dropdown-menu > li > ul > li .progress:after {
  content: " ";
  display: table;
}
.notifications.dropdown .dropdown-menu > li > ul > li .progress:after {
  clear: both;
}
.notifications.dropdown .dropdown-menu > li > ul > li:last-child {
  border-bottom: 0;
}
.notifications.dropdown .dropdown-menu > li > ul > li:first-child a:afterx {
  position: absolute;
  content: '';
  display: block;
  height: 10px;
  top: -10px;
  left: 0;
  right: 0;
  background: transparent;
  background: red;
  -webkit-box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.03);
  -moz-box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.03);
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.03);
}
.notifications.dropdown .dropdown-menu > li.external {
  background-color: #FFF;
  border-bottom: 0;
}
.notifications.dropdown .dropdown-menu > li.external a {
  color: #ec5956;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.notifications.dropdown .dropdown-menu > li.external a:hover {
  background-color: #fff;
  color: #e9423f;
}
body {
  font-family: "Helvetica Neue", Helvetica, "Noto Sans", sans-serif;
}
body.overflow-x-disabled {
  overflow-x: hidden;
}
a {
  color: #373e4a;
}
a:hover {
  text-decoration: none;
  color: #818da2;
}
.links-list {
  white-space: nowrap;
  padding-top: 14px;
  padding-bottom: 8px;
}
.links-list > li.sep {
  display: inline-block;
  border-left: 1px solid #eeeef0;
  height: 12px;
  width: 0px;
  line-height: 1.42857143;
  overflow: hidden;
  margin-left: 10px;
}
.scrollable {
  position: relative;
  overflow: hidden;
  height: 0;
}
.slimScrollDiv .slimScrollBar {
  cursor: pointer;
}
.clear {
  clear: both;
}
.knob {
  visibility: hidden;
}
canvas + .knob {
  visibility: visible;
}
.validate-has-error {
  border-color: #cc2424;
}
.validate-has-error:focus {
  border-color: #cc2424;
}
.validate-has-error + span.validate-has-error,
.validate-has-error > span.validate-has-error,
.input-group + .validate-has-error {
  color: #cc2424;
  display: inline-block;
  margin-top: 5px;
}
.validate-has-error .validate-has-error {
  color: #cc2424;
  display: inline-block;
  margin-top: 5px;
}
.make-switch + .validate-has-error {
  margin-left: 10px;
}
.validate-has-error .form-control {
  border-color: #cc2424;
}
.file-input-wrapper.inline {
  display: inline-block;
  width: auto;
}
.file2[type="file"] {
  visibility: hidden;
}
.file-input-wrapper .file2[type="file"] {
  visibility: visible;
}
.dist > * {
  margin-bottom: 10px;
}
.dist > *:before,
.dist > *:after {
  content: " ";
  display: table;
}
.dist > *:after {
  clear: both;
}
.dist > *:last-child {
  margin-bottom: 0;
}
@media print {
  .sidebar-menu {
    display: none !important;
  }
  .page-container.horizontal-menu {
    padding-top: 0;
  }
}
.nicescroll-rails > div {
  cursor: pointer;
}
.showcase-icon-list .icon-el a {
  position: relative;
  z-index: 1;
  background: #fafafa;
  margin-bottom: 6px;
  display: block;
  padding-right: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.showcase-icon-list .icon-el a i {
  margin-right: 5px;
  display: inline-block;
  padding: 6px 12px;
  vertical-align: middle;
  -webkit-border-radius: 3px 0 0 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px;
  background-clip: padding-box;
  background: #f0f0f0;
  font-size: 15px;
  width: 45px;
  text-align: center;
}
.showcase-icon-list .icon-el a:hover {
  z-index: 5;
  overflow: visible;
  padding: 5px 12px;
}
.showcase-icon-list .icon-el a:hover i {
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 60px;
  line-height: 50px;
  margin-right: 0px;
  color: #303641;
  font-size: 40px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
footer.main {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebebeb;
}
footer.main:before,
footer.main:after {
  content: " ";
  display: table;
}
footer.main:after {
  clear: both;
}
.padding-top-none {
  padding-top: 0 !important;
}
.padding-bottom-none {
  padding-bottom: 0 !important;
}
.margin-top-none {
  margin-top: 0 !important;
}
.padding-bottom-none {
  padding-bottom: 0 !important;
}
.margin-left {
  margin-left: 10px !important;
}
.margin-bottom {
  margin-bottom: 20px !important;
}
.bold {
  font-weight: bold !important;
}
.default-padding {
  padding: 0 15px !important;
}
img.full-width {
  width: 100%;
}
.form-groups-bordered > .form-group {
  border-bottom: 1px solid #ebebeb;
  padding-bottom: 15px;
  padding-top: 15px;
  margin-bottom: 0px;
}
.form-groups-bordered > .form-group:first-child {
  padding-top: 0;
}
.form-groups-bordered > .form-group:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: 0;
}
.morrischart {
  width: 100%;
}
.morrischart > svg {
  width: 100% !important;
}
.col-padding-1 {
  padding: 20px 20px 20px 40px !important;
}
.no-margin {
  margin: 0 !important;
}
.padding-lg {
  padding: 20px !important;
}
.border-top {
  border-top: 1px solid #ebebeb;
}
.no-transitions {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  transition: none !important;
}
#rickshaw-chart-demo {
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  overflow: hidden;
  padding-top: 200px;
}
#rickshaw-chart-demo svg {
  width: 100% !important;
}
#rickshaw-chart-demo .detail {
  left: -10px;
}
#rickshaw-chart-demo .rickshaw_legend {
  position: absolute;
  top: 0px;
  background: none;
}
#rickshaw-chart-demo .rickshaw_legend .label {
  color: rgba(48, 54, 65, 0.5);
}
#rickshaw-chart-demo-2 {
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  overflow: hidden;
  padding-top: 75px;
}
#rickshaw-chart-demo-2 svg {
  width: 100% !important;
}
#rickshaw-chart-demo-2 .detail {
  left: -10px;
}
#rickshaw-chart-demo-2 .rickshaw_legend {
  position: absolute;
  top: 0px;
  background: none;
}
#rickshaw-chart-demo-2 .rickshaw_legend .label {
  color: rgba(48, 54, 65, 0.5);
}
.theme-skins {
  background: #f5f5f6;
  border: none;
  margin: 0;
  padding: 0;
  margin-top: 12px;
  -webkit-border-radius: 2px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 2px;
  -moz-background-clip: padding;
  border-radius: 2px;
  background-clip: padding-box;
}
.theme-skins > li {
  border-bottom: 1px solid #ebebeb;
}
.theme-skins > li a {
  color: #555 !important;
  margin: 0;
  padding: 5px 10px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.theme-skins > li a i {
  display: inline-block;
  width: 15px;
  height: 13px;
  margin-right: 5px;
  vertical-align: middle;
  border: 1px solid transparent;
  position: relative;
  top: -2px;
}
.theme-skins > li a i.skin-main {
  background-color: #303641;
}
.theme-skins > li a i.skin-blue {
  background-color: #003471;
}
.theme-skins > li a i.skin-red {
  background-color: #981b1b;
}
.theme-skins > li a i.skin-black {
  background-color: #222222;
}
.theme-skins > li a i.skin-green {
  background-color: #00a651;
}
.theme-skins > li a i.skin-purple {
  background-color: #522b76;
}
.theme-skins > li a i.skin-yellow {
  background-color: #e8b51b;
}
.theme-skins > li a i.skin-cafe {
  background-color: #31271e;
}
.theme-skins > li a i.skin-white {
  background-color: #FFF;
  border-color: #ebebeb;
}
.theme-skins > li a:hover {
  background: rgba(235, 235, 235, 0.4);
}
.theme-skins > li.active a {
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
}
.theme-skins > li.caret {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  position: absolute;
  top: -4px;
  left: 57%;
  margin-left: -4px;
  border-top-color: #ebebeb;
  display: none;
}
.theme-skins > li.active a {
  background: rgba(235, 235, 235, 0.9);
  color: #737881;
  font-weight: bold;
}
.theme-skins > li.active a:hover {
  background: rgba(235, 235, 235, 0.9);
}
.theme-skins > li:last-child {
  border-bottom: 0;
}
.page-container.horizontal-menu header.navbar ul.nav > li.dropdown .theme-skins.dropdown-menu {
  width: 200px !important;
}
.version-highlights {
  margin: 0;
  padding: 0;
  border-top: 1px solid #ebebeb;
  margin-top: 30px;
  list-style: none;
  display: table;
}
.version-highlights li {
  display: table-row;
  vertical-align: middle;
  width: 100%;
  padding: 0;
}
.version-highlights li .screenshot,
.version-highlights li .notes {
  display: table-cell;
  vertical-align: middle;
}
.version-highlights li .screenshot {
  text-align: right;
  padding: 30px;
  width: 240px;
}
.version-highlights li .screenshot img {
  display: block;
  max-width: 100%;
  height: auto;
}
.thumbnail-highlight {
  border: 5px solid rgba(235, 235, 235, 0.5);
  -webkit-box-shadow: 0 0 0 1px #ebebeb;
  -moz-box-shadow: 0 0 0 1px #ebebeb;
  box-shadow: 0 0 0 1px #ebebeb;
}
@media (max-width: 768px) {
  .version-highlights {
    display: block;
  }
  .version-highlights li {
    display: block;
  }
  .version-highlights li .screenshot,
  .version-highlights li .notes {
    display: block;
  }
  .version-highlights li .screenshot {
    text-align: center;
    width: 100%;
  }
  .version-highlights li .screenshot img {
    display: inline-block;
  }
  .version-highlights li .notes {
    text-align: center;
    margin-bottom: 30px;
  }
}
.input-spinner {
  position: relative;
}
.input-spinner:before,
.input-spinner:after {
  content: " ";
  display: table;
}
.input-spinner:after {
  clear: both;
}
.input-spinner input,
.input-spinner button {
  float: left;
}
.input-spinner input {
  margin: 0;
  border-left: 0;
  border-right: 0;
  text-align: center;
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
  width: auto;
}
.input-spinner input.size-1 {
  width: 60px;
}
.input-spinner input.size-2 {
  width: 100px;
}
.input-spinner input.size-3 {
  width: 120px;
}
.input-spinner input.size-4 {
  width: 160px;
}
.input-spinner button {
  -webkit-border-radius: 3px 0 0 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px;
  background-clip: padding-box;
}
.input-spinner button + input + button {
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.panel > .panel-body.with-table {
  position: relative;
  padding: 0;
  margin: -1px;
  border: 0;
}
.panel > .panel-body.with-table > table {
  margin: 0;
}
.disabled-text {
  zoom: 1;
  filter: alpha(opacity=60);
  -webkit-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
}
.page-body.gray .page-container .main-content,
.page-body.gray .page-container {
  background: #f1f1f1;
}
.tile-stats.stat-tile {
  padding: 0px;
  height: 155px;
  border: none !important;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-neon-red {
  background: #ff4e50;
}
.tile-stats.stat-tile h3 {
  padding: 20px 20px 0px 20px;
}
.tile-stats.stat-tile p {
  padding: 0px 20px 20px 20px;
  margin-bottom: 20px;
}
.pie-chart {
  position: absolute;
  right: 20px;
  top: 35px;
}
.tile-group-2 {
  background: #FFF !important;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
}
.tile-group-2 .tile-white {
  background: #FFF !important;
  border: none !important;
  color: #303641 !important;
}
.tile-group-2 .tile-entry {
  background: none !important;
  border: none !important;
  color: #303641 !important;
}
.tile-group-2 .tile-white .tile-entry h3 {
  color: #2b303a;
}
.tile-group-2 .tile-white .tile-entry span {
  color: rgba(0, 0, 0, 0.4);
}
.country-list {
  width: 90%;
  padding: 50px 0 20px 0;
  padding-left: 30px;
}
.country-list li {
  border-top: 1px solid #EEE;
  padding: 12px 0;
  list-style: none;
}
.tile-group-2 .tile-right .map {
  margin: 0px !important;
}
.panel-table {
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  border: 0px !important;
}
.panel-table .panel-body {
  background: #FAFAFA;
}
.panel-table .panel-heading > .panel-title {
  padding: 30px 25px;
}
.panel-table .panel-heading > .panel-title span {
  color: #888888;
  font-size: 12px;
}
.panel-table th {
  font-weight: bold;
}
.calendar-widget td,
.calendar-widget th {
  text-align: center;
}
.calendar-widget th {
  padding: 12px 0;
}
@-moz-document url-prefix() {
  .page-container .tile-group .tile-left .map,
  .page-container .tile-group .tile-right .map {
    left: 40%;
    right: 16px;
  }
  .page-container .tile-stats .icon {
    bottom: 35px;
  }
}
.form-control + .description {
  display: block;
  margin-top: 5px;
  font-size: 11px;
}
.search-form-full {
  position: relative;
  margin-top: -2px;
}
.search-form-full .form-group {
  position: relative;
}
.search-form-full .form-group i {
  position: absolute;
  right: 10px;
  top: 50%;
  font-size: 14px;
  margin-top: -9px;
  color: rgba(148, 148, 148, 0.8);
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.search-form-full .form-group .form-control {
  margin-top: 12px;
  background: #f5f5f6;
  height: 38px;
  -webkit-box-shadow: inset 0 1px 0 rgba(0,0,0,.015);
  -moz-box-shadow: inset 0 1px 0 rgba(0,0,0,.015);
  box-shadow: inset 0 1px 0 rgba(0,0,0,.015);
}
.search-form-full .form-group .form-control:focus + i {
  color: #737881;
}
@media screen and (max-width: 480px) {
  .search-form-full {
    margin-top: 30px;
  }
}
.page-container .aciTree.aciBorder {
  border: 1px solid #ebebeb;
  min-height: 50px;
}
.neon-loading-bar {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  background: rgba(48, 54, 65, 0.3);
  height: 5px;
  z-index: 10000;
  top: 0px;
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.neon-loading-bar span {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0%;
  background: #ec5956;
}
.neon-loading-bar.progress-is-hidden {
  top: -5px;
  zoom: 1;
  filter: alpha(opacity=50);
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  visibility: hidden;
}
body {
  -webkit-perspective: 800;
  -moz-perspective: 800;
  perspective: 800;
  -webkit-perspective: 800px;
  -moz-perspective: 800px;
  perspective: 800px;
}
.datepicker.datepicker-dropdown {
  z-index: 10000 !important;
}
.top-dropdown-search .form-group {
  padding: 10px;
  border-bottom: 1px solid #EEE;
  margin-bottom: 0;
}
.page-body.loaded {
  -webkit-perspective: none;
  -moz-perspective: none;
  perspective: none;
}
.modal .modal-header .close {
  background: #f0f0f1;
  display: inline-block;
  padding: 0px 5px;
  line-height: 1;
  color: #787878;
  zoom: 1;
  filter: alpha(opacity=70);
  -webkit-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
  text-shadow: none;
  font-weight: 300;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.modal .modal-header .close:hover {
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.modal .modal-header h4 {
  text-shadow: none;
}
.modal .modal-content {
  border: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.well {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.modal-backdrop {
  z-index: 10000;
}
.modal-open .modal {
  z-index: 10001;
}
.page-body .select2-container .select2-choice .select2-arrow b {
  background: transparent !important;
}
.tabs-vertical-env {
  margin-bottom: 20px;
}
.tabs-vertical-env:before,
.tabs-vertical-env:after {
  content: " ";
  display: table;
}
.tabs-vertical-env:after {
  clear: both;
}
.tabs-vertical-env .tabs-vertical {
  margin-bottom: 0;
}
.tabs-vertical-env .tabs-vertical + .tab-content {
  margin-bottom: 0;
}
.tabs-vertical {
  float: left;
  background: #f5f5f6;
  width: 20%;
  border: 1px solid #ebebeb;
  margin-bottom: 20px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.tabs-vertical > li {
  border-bottom: 1px solid #ebebeb;
}
.tabs-vertical > li:first-child > a {
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.tabs-vertical > li:last-child {
  border-bottom: 0;
}
.tabs-vertical > li:last-child > a {
  -webkit-border-radius: 0 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 3px;
  background-clip: padding-box;
}
.tabs-vertical > li:hover > a {
  background-color: rgba(255, 255, 255, 0.3);
  color: #586376;
}
.tabs-vertical > li.active > a {
  display: block;
  position: relative;
  background-color: #ffffff;
  color: #373e4a;
}
.tabs-vertical > li.active > a:before {
  content: '';
  display: block;
  position: absolute;
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 20px 0 20px 13px;
  border-color: transparent transparent transparent #ebebeb;
  top: 0;
  right: -1px;
  margin-right: -12px;
  margin-top: -1px;
}
.tabs-vertical > li.active > a:after {
  content: '';
  display: block;
  position: absolute;
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 18px 0 18px 12px;
  border-color: transparent transparent transparent #fff;
  top: 2px;
  right: 0;
  margin-right: -12px;
  margin-top: -1px;
}
.tabs-vertical + .tab-content {
  float: right;
  width: 80%;
  padding-left: 25px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 20px;
}
.tabs-vertical.right-aligned {
  float: right;
}
.tabs-vertical.right-aligned > li.active > a:before,
.tabs-vertical.right-aligned > li.active > a:after {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  right: auto;
}
.tabs-vertical.right-aligned > li.active > a:before {
  left: -13px;
}
.tabs-vertical.right-aligned > li.active > a:after {
  left: -12px;
}
.tabs-vertical.right-aligned + .tab-content {
  padding-right: 25px;
  padding-left: 0;
}
blockquote.blockquote-default {
  background: #fff;
  border: 1px solid #ebebeb;
  border-left-width: 5px;
  padding: 15px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
blockquote.blockquote-blue,
blockquote.blockquote-info {
  background: #c5e8f7;
  border: 1px solid #b6d6e4;
  border-left-width: 5px;
  padding: 15px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  color: #0c3c50;
}
blockquote.blockquote-blue p,
blockquote.blockquote-info p,
blockquote.blockquote-blue small,
blockquote.blockquote-info small {
  color: #85a6b5;
}
blockquote.blockquote-blue p strong,
blockquote.blockquote-info p strong,
blockquote.blockquote-blue small strong,
blockquote.blockquote-info small strong {
  color: #0c3c50;
}
blockquote.blockquote-green,
blockquote.blockquote-success {
  background: #bdedbc;
  border: 1px solid #aedaad;
  border-left-width: 5px;
  padding: 15px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  color: #135012;
}
blockquote.blockquote-green p,
blockquote.blockquote-success p,
blockquote.blockquote-green small,
blockquote.blockquote-success small {
  color: #7baa7a;
}
blockquote.blockquote-green p strong,
blockquote.blockquote-success p strong,
blockquote.blockquote-green small strong,
blockquote.blockquote-success small strong {
  color: #135012;
}
blockquote.blockquote-gold,
blockquote.blockquote-warning {
  background: #ffefa4;
  border: 1px solid #ebdc97;
  border-left-width: 5px;
  padding: 15px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  color: #584b0d;
}
blockquote.blockquote-gold p,
blockquote.blockquote-warning p,
blockquote.blockquote-gold small,
blockquote.blockquote-warning small {
  color: #b7a96a;
}
blockquote.blockquote-gold p strong,
blockquote.blockquote-warning p strong,
blockquote.blockquote-gold small strong,
blockquote.blockquote-warning small strong {
  color: #584b0d;
}
blockquote.blockquote-red,
blockquote.blockquote-danger {
  background: #ffc9c9;
  border: 1px solid #ebb9b9;
  border-left-width: 5px;
  padding: 15px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  color: #4e1c1c;
}
blockquote.blockquote-red p,
blockquote.blockquote-danger p,
blockquote.blockquote-red small,
blockquote.blockquote-danger small {
  color: #b48585;
}
blockquote.blockquote-red p strong,
blockquote.blockquote-danger p strong,
blockquote.blockquote-red small strong,
blockquote.blockquote-danger small strong {
  color: #4e1c1c;
}
.alert-default {
  background-color: #ebebeb;
  border-color: #ebebeb;
  color: #303641;
}
.alert-default hr {
  border-top-color: #dedede;
}
.alert-default .alert-link {
  color: #1a1e24;
}
.alert-minimal {
  background-color: #ebebeb;
  border-color: #ebebeb;
  color: #303641;
  background-color: #ffffff;
}
.alert-minimal hr {
  border-top-color: #dedede;
}
.alert-minimal .alert-link {
  color: #1a1e24;
}
.form-control {
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
  outline: 0 !important;
}
.form-control.multi-select {
  visibility: hidden;
}
.form-group.has-warning .help-block,
.form-group.has-warning .control-label,
.form-group.has-warning .radio,
.form-group.has-warning .checkbox,
.form-group.has-warning .radio-inline,
.form-group.has-warning .checkbox-inline,
.form-group.has-warning.radio label,
.form-group.has-warning.checkbox label,
.form-group.has-warning.radio-inline label,
.form-group.has-warning.checkbox-inline label {
  color: #ffd40b;
}
.form-group.has-warning .form-control {
  border-color: #ffd78a;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.form-group.has-warning .form-control:focus {
  border-color: #ffc658;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #fffaf0;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #fffaf0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #fffaf0;
}
.form-group.has-warning .input-group-addon {
  color: #ffd40b;
  border-color: #ffd78a;
  background-color: #ffefa4;
}
.form-group.has-warning .form-control-feedback {
  color: #ffd40b;
}
.form-group.has-error .help-block,
.form-group.has-error .control-label,
.form-group.has-error .radio,
.form-group.has-error .checkbox,
.form-group.has-error .radio-inline,
.form-group.has-error .checkbox-inline,
.form-group.has-error.radio label,
.form-group.has-error.checkbox label,
.form-group.has-error.radio-inline label,
.form-group.has-error.checkbox-inline label {
  color: #ff3030;
}
.form-group.has-error .form-control {
  border-color: #ffafbd;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.form-group.has-error .form-control:focus {
  border-color: #ff7c92;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ffffff;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ffffff;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ffffff;
}
.form-group.has-error .input-group-addon {
  color: #ff3030;
  border-color: #ffafbd;
  background-color: #ffc9c9;
}
.form-group.has-error .form-control-feedback {
  color: #ff3030;
}
.form-group.has-success .help-block,
.form-group.has-success .control-label,
.form-group.has-success .radio,
.form-group.has-success .checkbox,
.form-group.has-success .radio-inline,
.form-group.has-success .checkbox-inline,
.form-group.has-success.radio label,
.form-group.has-success.checkbox label,
.form-group.has-success.radio-inline label,
.form-group.has-success.checkbox-inline label {
  color: #46cd43;
}
.form-group.has-success .form-control {
  border-color: #b4e8a8;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.form-group.has-success .form-control:focus {
  border-color: #91dd80;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #f9fdf8;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #f9fdf8;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #f9fdf8;
}
.form-group.has-success .input-group-addon {
  color: #46cd43;
  border-color: #b4e8a8;
  background-color: #bdedbc;
}
.form-group.has-success .form-control-feedback {
  color: #46cd43;
}
.input-group > span.twitter-typeahead {
  display: block !important;
}
.input-group > span.twitter-typeahead:before,
.input-group > span.twitter-typeahead:after {
  content: " ";
  display: table;
}
.input-group > span.twitter-typeahead:after {
  clear: both;
}
.dataTables_wrapper {
  background-color: #333;
  border: 1px solid #ebebeb;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.dataTables_wrapper > div {
  padding: 10px;
}
.dataTables_wrapper > div label {
  margin-bottom: 0;
  font-weight: normal;
  color: #949494;
}
.dataTables_wrapper > div.dataTables_length,
.dataTables_wrapper > div.dataTables_filter,
.dataTables_wrapper > div.dataTables_info,
.dataTables_wrapper > div.dataTables_processing,
.dataTables_wrapper > div.dataTables_paginate {
  color: #949494;
}
.dataTables_wrapper > div input[type="text"],
.dataTables_wrapper > div input[type="search"],
.dataTables_wrapper > div input[type="numbeer"] {
  background: #fff;
  border: 1px solid #ebebeb;
  padding: 6px 10px;
  outline: 0;
}
.dataTables_wrapper > div.dataTables_filter {
  padding-top: 15px;
}
.dataTables_wrapper > div .select2-container {
  display: inline-block;
  min-width: 85px;
  margin: 0 10px;
}
@media screen and (max-width: 768px) {
  .dataTables_wrapper > div .select2-container .select2-choice {
    padding-left: 0;
    padding-right: 15px;
  }
}
.dataTables_wrapper > table.dataTable {
  border-left: 0;
  border-right: 0;
}
.dataTables_wrapper > table.dataTable.no-footer {
  border-bottom: 1px solid #ebebeb;
}
.dataTables_wrapper > table.dataTable .sorting_disabled {
  padding-left: 10px;
}
.dataTables_wrapper > table.dataTable thead td,
.dataTables_wrapper > table.dataTable tbody td,
.dataTables_wrapper > table.dataTable tfoot td,
.dataTables_wrapper > table.dataTable thead th,
.dataTables_wrapper > table.dataTable tbody th,
.dataTables_wrapper > table.dataTable tfoot th {
  border-right: 0;
  border-bottom: 0;
}
.dataTables_wrapper > table.dataTable thead > tr > td:first-child,
.dataTables_wrapper > table.dataTable tbody > tr > td:first-child,
.dataTables_wrapper > table.dataTable tfoot > tr > td:first-child,
.dataTables_wrapper > table.dataTable thead > tr > th:first-child,
.dataTables_wrapper > table.dataTable tbody > tr > th:first-child,
.dataTables_wrapper > table.dataTable tfoot > tr > th:first-child {
  border-left: 0;
}
.dataTables_wrapper > table.dataTable tfoot th,
.dataTables_wrapper > table.dataTable tfoot td {
  border-top-color: #ebebeb;
}
.dataTables_wrapper .dataTables_paginate {
  border: 1px solid #ebebeb;
  padding: 0 !important;
  margin: 10px;
}
.dataTables_wrapper .dataTables_paginate .paginate_button {
  padding: 0;
  background-color: #fff;
  border: 0 !important;
  margin: 0 !important;
  color: #949494 !important;
  outline: 0;
  -webkit-border-radius: 0 !important;
  -webkit-background-clip: padding-box !important;
  -moz-border-radius: 0 !important;
  -moz-background-clip: padding !important;
  border-radius: 0 !important;
  background-clip: padding-box !important;
}
.dataTables_wrapper .dataTables_paginate .paginate_button.previous {
  border-right: 1px solid #ebebeb !important;
}
.dataTables_wrapper .dataTables_paginate .paginate_button:active {
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
}
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: #fafafa !important;
}
.page-body .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  color: #373e4a !important;
}
.dataTables_wrapper .dataTables_paginate span {
  padding: 0;
  margin: 0;
}
.dataTables_wrapper .dataTables_paginate span .ellipsis + .paginate_button {
  border-left: 1px solid #ebebeb !important;
}
.dataTables_wrapper .dataTables_paginate span .paginate_button {
  margin: 0;
  background: #fff !important;
  border-right: 1px solid #ebebeb !important;
}
.dataTables_wrapper .dataTables_paginate span .paginate_button.current {
  background: #262b34 !important;
  color: #fff !important;
}
.page-body .dataTables_wrapper .dataTables_paginate span .paginate_button.current:hover {
  color: #fff !important;
}
.dataTables_wrapper .dataTables_info {
  padding-top: 18px !important;
}
.dataTables_wrapper .dt-buttons .dt-button {
  background: #fff;
  border: 1px solid #ebebeb;
  margin: 0;
  font-size: 12px;
  -webkit-border-radius: 0px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0px;
  -moz-background-clip: padding;
  border-radius: 0px;
  background-clip: padding-box;
}
.dataTables_wrapper .dt-buttons .dt-button:hover {
  background: #fafafa;
  border-color: #ebebeb;
}
.dataTables_wrapper .dt-buttons .dt-button + .dt-button {
  border-left: 0;
}
.dataTables_wrapper .dt-buttons > .dt-button:first-child {
  -webkit-border-radius: 3px 0 0 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px;
  background-clip: padding-box;
}
.dataTables_wrapper .dt-buttons > .dt-button:last-child {
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
/*!
 * DataTables + Font Awesome integration
 * License: MIT - http://datatables.net/license
 */
table.dataTable thead th {
  position: relative;
  background-image: none !important;
  /* Remove the DataTables bootstrap integration styling */
}
table.dataTable thead th.sorting:after,
table.dataTable thead th.sorting_asc:after,
table.dataTable thead th.sorting_desc:after {
  position: absolute;
  top: 50% !important;
  right: 8px;
  display: block;
  font-family: Entypo;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
table.dataTable thead th.sorting:after {
  content: "\e834";
  color: #ddd;
  font-size: 0.8em;
  padding-top: 0.12em;
  -webkit-transform: translateY(-50%) rotate(90deg);
  -moz-transform: translateY(-50%) rotate(90deg);
  -ms-transform: translateY(-50%) rotate(90deg);
  -o-transform: translateY(-50%) rotate(90deg);
  transform: translateY(-50%) rotate(90deg);
}
table.dataTable thead th.sorting_asc:after {
  content: "\e876";
  -webkit-transform: translateY(-50%) rotate(0deg);
  -moz-transform: translateY(-50%) rotate(0deg);
  -ms-transform: translateY(-50%) rotate(0deg);
  -o-transform: translateY(-50%) rotate(0deg);
  transform: translateY(-50%) rotate(0deg);
}
table.dataTable thead th.sorting_desc:after {
  content: "\e873";
  -webkit-transform: translateY(-50%) rotate(0deg);
  -moz-transform: translateY(-50%) rotate(0deg);
  -ms-transform: translateY(-50%) rotate(0deg);
  -o-transform: translateY(-50%) rotate(0deg);
  transform: translateY(-50%) rotate(0deg);
}
div.dataTables_scrollBody table.dataTable thead th.sorting:after,
div.dataTables_scrollBody table.dataTable thead th.sorting_asc:after,
div.dataTables_scrollBody table.dataTable thead th.sorting_desc:after {
  content: "";
}
/* In Bootstrap and Foundation the padding top is a little different from the DataTables stylesheet */
table.table thead th.sorting:after,
table.table thead th.sorting_asc:after,
table.table thead th.sorting_desc:after {
  top: 8px;
}
/*
 * DataTables style pagination controls
 */
div.dataTables_paginate a.paginate_button.first,
div.dataTables_paginate a.paginate_button.previous {
  position: relative;
  padding-left: 24px;
}
div.dataTables_paginate a.paginate_button.next,
div.dataTables_paginate a.paginate_button.last {
  position: relative;
  padding-right: 24px;
}
div.dataTables_paginate a.first:before,
div.dataTables_paginate a.previous:before {
  position: absolute;
  top: 50%;
  left: 10px;
  display: block;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-family: Entypo;
}
div.dataTables_paginate a.next:after,
div.dataTables_paginate a.last:after {
  position: absolute;
  top: 50%;
  right: 10px;
  display: block;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-family: Entypo;
}
div.dataTables_paginate a.first:before {
  content: "\e880";
}
div.dataTables_paginate a.previous:before {
  content: "\e874";
}
div.e875 a.next:after,
div.dataTables_paginate a.next:after {
  content: "\e875";
}
div.dataTables_paginate a.last:after {
  content: "\e881";
}
/*
 * Bootstrap and foundation style pagination controls
 */
div.dataTables_paginate li.first > a,
div.dataTables_paginate li.previous > a {
  position: relative;
  padding-left: 24px;
}
div.dataTables_paginate li.next > a,
div.dataTables_paginate li.last > a {
  position: relative;
  padding-right: 24px;
}
div.dataTables_paginate li.first a:before,
div.dataTables_paginate li.previous a:before {
  position: absolute;
  top: 6px;
  left: 10px;
  display: block;
  font-family: Entypo;
}
div.dataTables_paginate li.next a:after,
div.dataTables_paginate li.last a:after {
  position: absolute;
  top: 6px;
  right: 10px;
  display: block;
  font-family: Entypo;
}
div.dataTables_paginate li.first a:before {
  content: "\f100";
}
div.dataTables_paginate li.previous a:before {
  content: "\e874";
}
div.dataTables_paginate li.next a:after {
  content: "\e875";
}
div.dataTables_paginate li.last a:after {
  content: "\e881";
}
/* In Foundation we don't want the padding like in bootstrap */
div.columns div.dataTables_paginate li.first a:before,
div.columns div.dataTables_paginate li.previous a:before,
div.columns div.dataTables_paginate li.next a:after,
div.columns div.dataTables_paginate li.last a:after {
  top: 0;
}
/*
.dataTables_wrapper-old {
	.clearfix;
	
	table {
		margin-bottom: 0;
		
		thead {
			
			tr {
				
				th {
					font-weight: bold;
					outline: none;
					cursor: default;
					.transall;
				
					&.sorting_asc:before,
					&.sorting_desc:before {
						display: block;
						float: right;
						color: @main_color;
						font-family: @font_entypo;
						content: '\e876';
					}
						
					&.sorting_desc:before {
						content: '\e873';
					}
					
					&:active {
						outline: none;
					}
					
					&.sorting_asc, &.sorting_desc {
						color: @main_color;
					}
				}
			}
		}
		
		tfoot {
			
			tr {
				
				th {
					font-weight: bold;
				}
			}
		}
		
		+ .row {
			margin-bottom: 20px;
		}
	}
	
	> table:first-child {
		.border-radius(@border_radius @border_radius 0 0);
	}
	
	a {
		cursor: pointer;
		outline: none;
	}
	
	.col-left {
		padding-right: 0;
	}
	
	.col-right {
		padding-left: 0;
	}
	
	// Table Header
	.dataTables_length, .dataTables_filter {
		background: #fff;
		border: 1px solid @default;
		border-bottom: 0;
		padding: @base_margin/2 @padding-base-horizontal;
		height: 58px;
		.clearfix;
		
	}
	
	.dataTables_length {
		border-right: 0;
		.border-radius(@border_radius 0 0 0);
		
		> label {
			white-space: nowrap;
			margin-top: -7px;
			
			.select2-container {
				display: inline-block;
				width: auto !important;
				min-width: 87px;
			}
		}
	}
	
	.dataTables_filter {
		border-left: 0;
		padding-top: @base_padding + 3;
		.border-radius(0 @border_radius 0 0);
		
		> label {
			float: right;
			display: block;
			white-space: nowrap;
			
			input {
				.form-control;
				display: inline-block;
				width: 150px;
				margin-left: @base_padding/2;
			}
		}
	}
	
	.select2-container {
		display: inline-block !important;
		margin-left: @base_padding/2;
		margin-right: @base_padding/2;
		@h: 25px;
		
		.select2-choice {
			height: @h;
			line-height: @h - 1;
			
			.select2-chosen {
				padding-right: @base_margin/2;
			}
			
			.select2-arrow {
				width: 25px;
			}
		}
	}
	
	
	// Table Footer
	.dataTables_info, .dataTables_paginate {
		padding: @base_padding @padding-base-horizontal;
		border: 1px solid @default;
		border-top: 0;
		background: darken(#fff, 2%);
		height: 47px;
		.clearfix;
		
		&.dataTables_info {
			border-right: 0;
			.border-radius(0 0 0 @border_radius);
			line-height: 30px;
		}
		
		&.dataTables_paginate {
			border-left: 0;
			.border-radius(0 0 @border_radius 0);
			text-align: right;
			
			.pagination {
				margin: 0;
			}
		}
	}
	
	
	// Table Tools
	
	.export-data {
		position: absolute;
		right: 250px;
		top: 13px;
	}
}



.page-body .datatable.table {
	
	tbody {
		
		td, th {
			vertical-align: middle;
		}
	}
}


table {
	
	.replace-inputs {
		
		input {
			.form-control;
		}
	}
}



// v1.5.2
.dataTables_wrapper {
	
	.dataTable {
		width: 100% !important;
	}
}*/
/* Table Tools */
.chat-visible #chat {
  display: table-cell;
  vertical-align: top;
}
.horizontal-menu #chat {
  top: 61px;
  border-top: 1px solid #454a54;
}
.horizontal-menu.chat-visible .main-content > .container {
  width: 100% !important;
}
.chat-notifications-badge {
  margin-left: 5px;
}
.chat-notifications-badge.is-hidden {
  display: none;
}
#chat {
  position: absolute;
  position: relative;
  right: 0;
  top: 0;
  bottom: 0;
  width: 280px;
  background: #303641;
  z-index: 5;
  display: none;
}
#chat.fixed {
  min-height: 0 !important;
}
#chat .chat-inner {
  overflow: auto;
  height: 100%;
}
#chat .chat-header {
  font-size: 16px;
  color: #7f8186;
  padding: 30px 35px;
  line-height: 1;
  margin: 0;
  border-bottom: 1px solid #343a45;
  position: relative;
}
#chat .chat-header .badge {
  position: relative;
  top: -1px;
  margin-left: 5px;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
#chat .chat-header .badge.is-hidden {
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}
#chat .chat-header .chat-close {
  position: absolute;
  right: 0px;
  top: 0px;
  font-size: 15px;
  top: 50%;
  right: 25px;
  margin-top: -7px;
  color: #bec0c2;
  zoom: 1;
  filter: alpha(opacity=50);
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.transall #chat .chat-header .chat-close:hover {
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
#chat .chat-group {
  margin-top: 30px;
}
#chat .chat-group > strong,
#chat .chat-group > a {
  display: block;
  padding: 6px 35px;
}
#chat .chat-group > strong {
  text-transform: uppercase;
  color: #7f8186;
}
#chat .chat-group > a {
  position: relative;
  color: #bec0c2;
  -webkit-transition: background 250ms ease-in-out;
  -moz-transition: background 250ms ease-in-out;
  -o-transition: background 250ms ease-in-out;
  transition: background 250ms ease-in-out;
}
#chat .chat-group > a:hover,
#chat .chat-group > a.active {
  background-color: #343a45;
}
#chat .chat-group > a:before {
  content: '';
  display: block;
  position: absolute;
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 4px 0 4px 4px;
  border-color: transparent transparent transparent #2b303a;
  left: 0;
  top: 50%;
  margin-top: -4px;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
#chat .chat-group > a.active:before {
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
#chat .chat-group > a .badge {
  font-size: 9px;
  margin-left: 5px;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
#chat .chat-group > a .badge.is-hidden {
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}
#chat .chat-group > a em {
  font-style: normal;
}
#chat .user-status {
  display: inline-block;
  background: #575d67;
  margin-right: 5px;
  width: 8px;
  height: 8px;
  -webkit-border-radius: 8px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 8px;
  -moz-background-clip: padding;
  border-radius: 8px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
#chat .user-status.is-online {
  background-color: #06b53c;
}
#chat .user-status.is-offline {
  background-color: #575d67;
}
#chat .user-status.is-idle {
  background-color: #f7d227;
}
#chat .user-status.is-busy {
  background-color: #ee4749;
}
#chat .chat-conversation {
  position: absolute;
  right: 280px;
  width: 340px;
  background: #2b303a;
  -webkit-border-radius: 3px 0 0 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px;
  background-clip: padding-box;
  display: none;
  opacity: 0;
}
#chat .chat-conversation .conversation-header {
  padding: 20px 24px;
  font-size: 14px;
  color: #fff;
  border-bottom: 1px solid #343a45;
}
#chat .chat-conversation .conversation-header small {
  color: rgba(190, 192, 194, 0.6);
  font-size: 12px;
  padding-left: 8px;
}
#chat .chat-conversation .conversation-header .conversation-close {
  float: right;
  color: #7f8186;
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  position: relative;
  top: 3px;
}
#chat .chat-conversation .conversation-body {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: auto;
  height: 250px;
}
#chat .chat-conversation .conversation-body::-webkit-scrollbar {
  width: 5px;
}
#chat .chat-conversation .conversation-body::-webkit-scrollbar-track {
  width: 5px;
  background-color: #2b303a;
}
#chat .chat-conversation .conversation-body::-webkit-scrollbar-thumb {
  background-color: rgba(52, 58, 69, 0.8);
}
#chat .chat-conversation .conversation-body > li {
  padding: 20px 24px;
  padding-top: 15px;
  padding-bottom: 15px;
}
#chat .chat-conversation .conversation-body > li:before,
#chat .chat-conversation .conversation-body > li:after {
  content: " ";
  display: table;
}
#chat .chat-conversation .conversation-body > li:after {
  clear: both;
}
#chat .chat-conversation .conversation-body > li.odd,
#chat .chat-conversation .conversation-body > li.even,
#chat .chat-conversation .conversation-body > li.opponent {
  background: #343a45;
}
#chat .chat-conversation .conversation-body > li.unread {
  background: #3d4451;
}
#chat .chat-conversation .conversation-body > li .user {
  font-weight: bold;
  color: #fff;
}
#chat .chat-conversation .conversation-body > li .time {
  float: right;
  font-style: italic;
  color: rgba(190, 192, 194, 0.8);
  font-size: 11px;
}
#chat .chat-conversation .chat-textarea {
  padding: 20px 24px;
  position: relative;
}
#chat .chat-conversation .chat-textarea:after {
  content: '\e83c';
  font-family: "Entypo";
  color: #bec0c2;
  right: 35px;
  top: 25px;
  font-size: 15px;
  position: absolute;
}
#chat .chat-conversation .chat-textarea textarea {
  background: #343a45;
  border: 1px solid #343a45;
  color: #fff;
  max-height: 100px !important;
  padding-right: 35px;
  height: 32px;
}
#chat .chat-conversation .chat-textarea textarea::-moz-placeholder {
  color: rgba(190, 192, 194, 0.85);
  opacity: 1;
}
#chat .chat-conversation .chat-textarea textarea:-ms-input-placeholder {
  color: rgba(190, 192, 194, 0.85);
}
#chat .chat-conversation .chat-textarea textarea::-webkit-input-placeholder {
  color: rgba(190, 192, 194, 0.85);
}
#chat .chat-conversation .chat-textarea textarea::-webkit-input-placeholder {
  color: rgba(190, 192, 194, 0.85);
}
#chat .chat-conversation .chat-textarea textarea:-moz-placeholder {
  color: rgba(190, 192, 194, 0.85);
}
#chat .chat-conversation .chat-textarea textarea::-moz-placeholder {
  color: rgba(190, 192, 194, 0.85);
}
#chat .chat-conversation .chat-textarea textarea:-ms-input-placeholder {
  color: rgba(190, 192, 194, 0.85);
}
#chat .chat-conversation .chat-textarea textarea::-webkit-scrollbar {
  width: 5px;
}
#chat .chat-conversation .chat-textarea textarea::-webkit-scrollbar-track {
  width: 5px;
  background-color: #343a45;
}
#chat .chat-conversation .chat-textarea textarea::-webkit-scrollbar-thumb {
  background-color: #4a5262;
}
.chat-history {
  display: none;
}
/* Relatively-Small screen */
@media (max-width: 767px) {
  .chat-visible #chat,
  .chat-visible #chat.fixed {
    display: none;
  }
  .chat-visible.toggle-click #chat,
  .chat-visible.toggle-click #chat.fixed {
    display: block;
  }
  #chat,
  #chat.fixed {
    position: fixed;
    width: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 10px;
    -webkit-border-radius: 3px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px;
    -moz-background-clip: padding;
    border-radius: 3px;
    background-clip: padding-box;
  }
  #chat .chat-conversation,
  #chat.fixed .chat-conversation {
    position: fixed;
    width: auto;
    left: 0;
    right: 0;
    top: 0 !important;
    bottom: 0;
    margin: 10px;
    overflow: auto;
    -webkit-border-radius: 3px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 3px;
    -moz-background-clip: padding;
    border-radius: 3px;
    background-clip: padding-box;
  }
  #chat .chat-conversation .conversation-body,
  #chat.fixed .chat-conversation .conversation-body {
    height: auto;
    max-height: 450px;
  }
  #chat .chat-conversation::-webkit-scrollbar,
  #chat.fixed .chat-conversation::-webkit-scrollbar {
    width: 5px;
  }
  #chat .chat-conversation::-webkit-scrollbar-track,
  #chat.fixed .chat-conversation::-webkit-scrollbar-track {
    width: 5px;
    background-color: #343a45;
  }
  #chat .chat-conversation::-webkit-scrollbar-thumb,
  #chat.fixed .chat-conversation::-webkit-scrollbar-thumb {
    background-color: #4a5262;
  }
}
.tile-stats {
  position: relative;
  display: block;
  background: #303641;
  padding: 20px;
  margin-bottom: 10px;
  overflow: hidden;
  -webkit-border-radius: 5px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 5px;
  -moz-background-clip: padding;
  border-radius: 5px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.tile-stats:hover {
  background: #252a32;
}
.tile-stats .icon {
  color: rgba(0, 0, 0, 0.1);
  position: absolute;
  right: 5px;
  bottom: 5px;
  z-index: 1;
}
.tile-stats .icon i {
  font-size: 100px;
  line-height: 0;
  margin: 0;
  padding: 0;
  vertical-align: bottom;
}
.tile-stats .icon i:before {
  margin: 0;
  padding: 0;
  line-height: 0;
}
.tile-stats .num,
.tile-stats h3,
.tile-stats p {
  position: relative;
  color: #fff;
  z-index: 5;
  margin: 0;
  padding: 0;
}
.tile-stats .num {
  font-size: 18px;
  font-weight: bold;
}
.tile-stats h3 {
  font-size: 18px;
  margin-top: 5px;
}
.tile-stats p {
  font-size: 11px;
  margin-top: 5px;
}
.tile-stats.tile-red {
  background: #f56954;
}
.tile-stats.tile-red:hover {
  background: #f4543c;
}
.tile-stats.tile-red .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-red .num,
.tile-stats.tile-red h3,
.tile-stats.tile-red p {
  color: #fff;
}
.tile-stats.tile-green {
  background: #00a65a;
}
.tile-stats.tile-green:hover {
  background: #008d4c;
}
.tile-stats.tile-green .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-green .num,
.tile-stats.tile-green h3,
.tile-stats.tile-green p {
  color: #fff;
}
.tile-stats.tile-blue {
  background: #0073b7;
}
.tile-stats.tile-blue:hover {
  background: #00639e;
}
.tile-stats.tile-blue .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-blue .num,
.tile-stats.tile-blue h3,
.tile-stats.tile-blue p {
  color: #fff;
}
.tile-stats.tile-aqua {
  background: #00c0ef;
}
.tile-stats.tile-aqua:hover {
  background: #00acd6;
}
.tile-stats.tile-aqua .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-aqua .num,
.tile-stats.tile-aqua h3,
.tile-stats.tile-aqua p {
  color: #fff;
}
.tile-stats.tile-cyan {
  background: #00b29e;
}
.tile-stats.tile-cyan:hover {
  background: #009987;
}
.tile-stats.tile-cyan .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-cyan .num,
.tile-stats.tile-cyan h3,
.tile-stats.tile-cyan p {
  color: #fff;
}
.tile-stats.tile-purple {
  background: #ba79cb;
}
.tile-stats.tile-purple:hover {
  background: #b167c4;
}
.tile-stats.tile-purple .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-purple .num,
.tile-stats.tile-purple h3,
.tile-stats.tile-purple p {
  color: #fff;
}
.tile-stats.tile-pink {
  background: #ec3b83;
}
.tile-stats.tile-pink:hover {
  background: #ea2474;
}
.tile-stats.tile-pink .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-pink .num,
.tile-stats.tile-pink h3,
.tile-stats.tile-pink p {
  color: #fff;
}
.tile-stats.tile-orange {
  background: #ffa812;
}
.tile-stats.tile-orange:hover {
  background: #f89d00;
}
.tile-stats.tile-orange .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-orange .num,
.tile-stats.tile-orange h3,
.tile-stats.tile-orange p {
  color: #fff;
}
.tile-stats.tile-brown {
  background: #6c541e;
}
.tile-stats.tile-brown:hover {
  background: #584418;
}
.tile-stats.tile-brown .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-brown .num,
.tile-stats.tile-brown h3,
.tile-stats.tile-brown p {
  color: #fff;
}
.tile-stats.tile-plum {
  background: #701c1c;
}
.tile-stats.tile-plum:hover {
  background: #5c1717;
}
.tile-stats.tile-plum .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-plum .num,
.tile-stats.tile-plum h3,
.tile-stats.tile-plum p {
  color: #fff;
}
.tile-stats.tile-gray {
  background: #f5f5f5;
}
.tile-stats.tile-gray:hover {
  background: #e8e8e8;
}
.tile-stats.tile-gray .icon {
  color: rgba(0, 0, 0, 0.1);
}
.tile-stats.tile-gray .num,
.tile-stats.tile-gray h3,
.tile-stats.tile-gray p {
  color: #8f8f8f;
}
.tile-stats.tile-white {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white .num,
.tile-stats.tile-white h3,
.tile-stats.tile-white p {
  color: #303641;
}
.tile-stats.tile-white:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-red {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-red:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-red .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-red .num,
.tile-stats.tile-white-red h3,
.tile-stats.tile-white-red p {
  color: #f56954;
}
.tile-stats.tile-white-red:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-green {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-green:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-green .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-green .num,
.tile-stats.tile-white-green h3,
.tile-stats.tile-white-green p {
  color: #00a65a;
}
.tile-stats.tile-white-green:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-blue {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-blue:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-blue .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-blue .num,
.tile-stats.tile-white-blue h3,
.tile-stats.tile-white-blue p {
  color: #0073b7;
}
.tile-stats.tile-white-blue:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-aqua {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-aqua:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-aqua .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-aqua .num,
.tile-stats.tile-white-aqua h3,
.tile-stats.tile-white-aqua p {
  color: #00c0ef;
}
.tile-stats.tile-white-aqua:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-cyan {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-cyan:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-cyan .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-cyan .num,
.tile-stats.tile-white-cyan h3,
.tile-stats.tile-white-cyan p {
  color: #00b29e;
}
.tile-stats.tile-white-cyan:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-purple {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-purple:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-purple .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-purple .num,
.tile-stats.tile-white-purple h3,
.tile-stats.tile-white-purple p {
  color: #ba79cb;
}
.tile-stats.tile-white-purple:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-pink {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-pink:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-pink .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-pink .num,
.tile-stats.tile-white-pink h3,
.tile-stats.tile-white-pink p {
  color: #ec3b83;
}
.tile-stats.tile-white-pink:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-orange {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-orange:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-orange .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-orange .num,
.tile-stats.tile-white-orange h3,
.tile-stats.tile-white-orange p {
  color: #ffa812;
}
.tile-stats.tile-white-orange:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-brown {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-brown:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-brown .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-brown .num,
.tile-stats.tile-white-brown h3,
.tile-stats.tile-white-brown p {
  color: #6c541e;
}
.tile-stats.tile-white-brown:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-plum {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-plum:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-plum .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-plum .num,
.tile-stats.tile-white-plum h3,
.tile-stats.tile-white-plum p {
  color: #701c1c;
}
.tile-stats.tile-white-plum:hover {
  background-color: #fafafa;
}
.tile-stats.tile-white-gray {
  background: #fff;
  border: 1px solid #ebebeb;
}
.tile-stats.tile-white-gray:hover {
  background: #f2f2f2;
}
.tile-stats.tile-white-gray .icon {
  color: #f2f2f2;
}
.tile-stats.tile-white-gray .num,
.tile-stats.tile-white-gray h3,
.tile-stats.tile-white-gray p {
  color: #8f8f8f;
}
.tile-stats.tile-white-gray:hover {
  background-color: #fafafa;
}
.tile-title {
  position: relative;
  display: block;
  background: transparent;/*#303641;*/
  margin-bottom: 10px;
  -webkit-border-radius: 5px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 5px;
  -moz-background-clip: padding;
  border-radius: 5px;
  background-clip: padding-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
/*.tile-title:hover {
    border: 1px solid #888;
    box-shadow: 0 0 5px white;
}*/
.tile-title .icon {
  text-align: center;
  padding: 20px;
}
.tile-title .icon i {
  font-size: 60px;
  line-height: 1;
  margin: 0;
  padding: 0;
  vertical-align: middle;
}
.tile-title .icon i:before {
  margin: 0;
  padding: 0;
  line-height: 1;
}

.tile-title .icon i,
.tile-title h3,
.tile-title p {
  color: #fff;
}
.tile-title .title {
  background: transparent;/*#252a32;*/
  text-align: center;
}
.tile-title .title h3,
.tile-title .title p {
  margin: 0;
  padding: 0 20px;
}
.tile-title .title h3 {
  padding-top: 20px;
  font-size: 16px;
  font-weight: bold;
}
.tile-title .title p {
  padding-bottom: 20px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-red {
  background: #f56954;
}
.tile-title.tile-red:hover {
  background-color: #f4543c;
}
.tile-title.tile-red .icon i,
.tile-title.tile-red h3,
.tile-title.tile-red p {
  color: #fff;
}
.tile-title.tile-red .icon i {
  color: #fff;
}
.tile-title.tile-red .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-red .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-green {
  background: #00a65a;
}
.tile-title.tile-green:hover {
  background-color: #008d4c;
}
.tile-title.tile-green .icon i,
.tile-title.tile-green h3,
.tile-title.tile-green p {
  color: #fff;
}
.tile-title.tile-green .icon i {
  color: #fff;
}
.tile-title.tile-green .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-green .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-blue {
  background: #0073b7;
}
.tile-title.tile-blue:hover {
  background-color: #00639e;
}
.tile-title.tile-blue .icon i,
.tile-title.tile-blue h3,
.tile-title.tile-blue p {
  color: #fff;
}
.tile-title.tile-blue .icon i {
  color: #fff;
}
.tile-title.tile-blue .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-blue .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-aqua {
  background: #00c0ef;
}
.tile-title.tile-aqua:hover {
  background-color: #00acd6;
}
.tile-title.tile-aqua .icon i,
.tile-title.tile-aqua h3,
.tile-title.tile-aqua p {
  color: #fff;
}
.tile-title.tile-aqua .icon i {
  color: #fff;
}
.tile-title.tile-aqua .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-aqua .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-cyan {
  background: #00b29e;
}
.tile-title.tile-cyan:hover {
  background-color: #009987;
}
.tile-title.tile-cyan .icon i,
.tile-title.tile-cyan h3,
.tile-title.tile-cyan p {
  color: #fff;
}
.tile-title.tile-cyan .icon i {
  color: #fff;
}
.tile-title.tile-cyan .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-cyan .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-purple {
  background: #ba79cb;
}
.tile-title.tile-purple:hover {
  background-color: #b167c4;
}
.tile-title.tile-purple .icon i,
.tile-title.tile-purple h3,
.tile-title.tile-purple p {
  color: #fff;
}
.tile-title.tile-purple .icon i {
  color: #fff;
}
.tile-title.tile-purple .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-purple .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-pink {
  background: #ec3b83;
}
.tile-title.tile-pink:hover {
  background-color: #ea2474;
}
.tile-title.tile-pink .icon i,
.tile-title.tile-pink h3,
.tile-title.tile-pink p {
  color: #fff;
}
.tile-title.tile-pink .icon i {
  color: #fff;
}
.tile-title.tile-pink .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-pink .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-orange {
  background: #ffa812;
}
.tile-title.tile-orange:hover {
  background-color: #f89d00;
}
.tile-title.tile-orange .icon i,
.tile-title.tile-orange h3,
.tile-title.tile-orange p {
  color: #fff;
}
.tile-title.tile-orange .icon i {
  color: #fff;
}
.tile-title.tile-orange .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-orange .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-brown {
  background: #6c541e;
}
.tile-title.tile-brown:hover {
  background-color: #584418;
}
.tile-title.tile-brown .icon i,
.tile-title.tile-brown h3,
.tile-title.tile-brown p {
  color: #fff;
}
.tile-title.tile-brown .icon i {
  color: #fff;
}
.tile-title.tile-brown .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-brown .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-plum {
  background: #701c1c;
}
.tile-title.tile-plum:hover {
  background-color: #5c1717;
}
.tile-title.tile-plum .icon i,
.tile-title.tile-plum h3,
.tile-title.tile-plum p {
  color: #fff;
}
.tile-title.tile-plum .icon i {
  color: #fff;
}
.tile-title.tile-plum .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-plum .title p {
  color: rgba(255, 255, 255, 0.85);
}
.tile-title.tile-gray {
  background: #f5f5f5;
}
.tile-title.tile-gray:hover {
  background-color: #e8e8e8;
}
.tile-title.tile-gray .icon i,
.tile-title.tile-gray h3,
.tile-title.tile-gray p {
  color: #8f8f8f;
}
.tile-title.tile-gray .icon i {
  color: #8f8f8f;
}
.tile-title.tile-gray .title {
  background: rgba(0, 0, 0, 0.1);
}
.tile-title.tile-gray .title p {
  color: rgba(143, 143, 143, 0.85);
}
.tile-block {
  background: #00a65b;
  -webkit-border-radius: 5px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 5px;
  -moz-background-clip: padding;
  border-radius: 5px;
  background-clip: padding-box;
  margin-bottom: 20px;
  background-color: #303641;
  color: #fff;
}
.tile-block:before,
.tile-block:after {
  content: " ";
  display: table;
}
.tile-block:after {
  clear: both;
}
.tile-block .tile-header {
  color: #fff;
  font-size: 17px;
  padding: 20px;
}
.tile-block .tile-header a,
.tile-block .tile-header span {
  color: #fff;
}
.tile-block .tile-header span {
  display: block;
  margin-top: 4px;
  font-size: 11px;
}
.tile-block .tile-header i {
  float: right;
  font-size: 28px;
  position: relative;
  top: 4px;
}
.tile-block .tile-header i[class^="entypo-"] {
  top: 0;
}
.tile-block .tile-content {
  background: rgba(0, 0, 0, 0.03);
  padding: 20px;
}
.tile-block .tile-content .todo-list .neon-cb-replacement .cb-wrapper + label {
  top: -3px;
  margin-left: 8px;
}
.tile-block .tile-footer {
  background: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px;
  color: #fff;
  -webkit-border-radius: 0 0 5px 5px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 5px 5px;
  -moz-background-clip: padding;
  border-radius: 0 0 5px 5px;
  background-clip: padding-box;
}
.tile-block .tile-footer a {
  color: #fff;
  font-weight: bold;
}
.tile-block .form-control,
.tile-block .btn {
  background: rgba(0, 0, 0, 0.15);
  color: #fff;
  border-color: transparent;
}
.tile-block .form-control::-moz-placeholder,
.tile-block .btn::-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
  opacity: 1;
}
.tile-block .form-control:-ms-input-placeholder,
.tile-block .btn:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.tile-block .form-control::-webkit-input-placeholder,
.tile-block .btn::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.tile-block .form-control::-webkit-input-placeholder,
.tile-block .btn::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.tile-block .form-control:-moz-placeholder,
.tile-block .btn:-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.tile-block .form-control::-moz-placeholder,
.tile-block .btn::-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.tile-block .form-control:-ms-input-placeholder,
.tile-block .btn:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.tile-block .form-control + .todo-list,
.tile-block .btn + .todo-list {
  margin-top: 20px;
}
.tile-block .form-control + .btn {
  margin-top: 10px;
}
.tile-block .tile-header a,
.tile-block .tile-header span,
.tile-block .tile-header i {
  color: #fff;
}
.tile-block .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #303641;
}
.tile-block .tile-footer {
  color: #fff;
}
.tile-block .tile-footer a {
  color: #fff;
}
.tile-block.tile-red {
  background-color: #f56954;
  color: #fff;
}
.tile-block.tile-red .tile-header a,
.tile-block.tile-red .tile-header span,
.tile-block.tile-red .tile-header i {
  color: #fff;
}
.tile-block.tile-red .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #f56954;
}
.tile-block.tile-red .tile-footer {
  color: #fff;
}
.tile-block.tile-red .tile-footer a {
  color: #fff;
}
.tile-block.tile-green {
  background-color: #00a65a;
  color: #fff;
}
.tile-block.tile-green .tile-header a,
.tile-block.tile-green .tile-header span,
.tile-block.tile-green .tile-header i {
  color: #fff;
}
.tile-block.tile-green .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #00a65a;
}
.tile-block.tile-green .tile-footer {
  color: #fff;
}
.tile-block.tile-green .tile-footer a {
  color: #fff;
}
.tile-block.tile-blue {
  background-color: #0073b7;
  color: #fff;
}
.tile-block.tile-blue .tile-header a,
.tile-block.tile-blue .tile-header span,
.tile-block.tile-blue .tile-header i {
  color: #fff;
}
.tile-block.tile-blue .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #0073b7;
}
.tile-block.tile-blue .tile-footer {
  color: #fff;
}
.tile-block.tile-blue .tile-footer a {
  color: #fff;
}
.tile-block.tile-aqua {
  background-color: #00c0ef;
  color: #fff;
}
.tile-block.tile-aqua .tile-header a,
.tile-block.tile-aqua .tile-header span,
.tile-block.tile-aqua .tile-header i {
  color: #fff;
}
.tile-block.tile-aqua .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #00c0ef;
}
.tile-block.tile-aqua .tile-footer {
  color: #fff;
}
.tile-block.tile-aqua .tile-footer a {
  color: #fff;
}
.tile-block.tile-cyan {
  background-color: #00b29e;
  color: #fff;
}
.tile-block.tile-cyan .tile-header a,
.tile-block.tile-cyan .tile-header span,
.tile-block.tile-cyan .tile-header i {
  color: #fff;
}
.tile-block.tile-cyan .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #00b29e;
}
.tile-block.tile-cyan .tile-footer {
  color: #fff;
}
.tile-block.tile-cyan .tile-footer a {
  color: #fff;
}
.tile-block.tile-purple {
  background-color: #ba79cb;
  color: #fff;
}
.tile-block.tile-purple .tile-header a,
.tile-block.tile-purple .tile-header span,
.tile-block.tile-purple .tile-header i {
  color: #fff;
}
.tile-block.tile-purple .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #ba79cb;
}
.tile-block.tile-purple .tile-footer {
  color: #fff;
}
.tile-block.tile-purple .tile-footer a {
  color: #fff;
}
.tile-block.tile-pink {
  background-color: #ec3b83;
  color: #fff;
}
.tile-block.tile-pink .tile-header a,
.tile-block.tile-pink .tile-header span,
.tile-block.tile-pink .tile-header i {
  color: #fff;
}
.tile-block.tile-pink .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #ec3b83;
}
.tile-block.tile-pink .tile-footer {
  color: #fff;
}
.tile-block.tile-pink .tile-footer a {
  color: #fff;
}
.tile-block.tile-orange {
  background-color: #ffa812;
  color: #fff;
}
.tile-block.tile-orange .tile-header a,
.tile-block.tile-orange .tile-header span,
.tile-block.tile-orange .tile-header i {
  color: #fff;
}
.tile-block.tile-orange .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #ffa812;
}
.tile-block.tile-orange .tile-footer {
  color: #fff;
}
.tile-block.tile-orange .tile-footer a {
  color: #fff;
}
.tile-block.tile-brown {
  background-color: #6c541e;
  color: #fff;
}
.tile-block.tile-brown .tile-header a,
.tile-block.tile-brown .tile-header span,
.tile-block.tile-brown .tile-header i {
  color: #fff;
}
.tile-block.tile-brown .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #6c541e;
}
.tile-block.tile-brown .tile-footer {
  color: #fff;
}
.tile-block.tile-brown .tile-footer a {
  color: #fff;
}
.tile-block.tile-plum {
  background-color: #701c1c;
  color: #fff;
}
.tile-block.tile-plum .tile-header a,
.tile-block.tile-plum .tile-header span,
.tile-block.tile-plum .tile-header i {
  color: #fff;
}
.tile-block.tile-plum .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #701c1c;
}
.tile-block.tile-plum .tile-footer {
  color: #fff;
}
.tile-block.tile-plum .tile-footer a {
  color: #fff;
}
.tile-block.tile-gray {
  background-color: #f5f5f5;
  color: #767676;
}
.tile-block.tile-gray .tile-header a,
.tile-block.tile-gray .tile-header span,
.tile-block.tile-gray .tile-header i {
  color: #767676;
}
.tile-block.tile-gray .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #f5f5f5;
}
.tile-block.tile-gray .tile-footer {
  color: #767676;
}
.tile-block.tile-gray .tile-footer a {
  color: #767676;
}
.tile-block.tile-gray .tile-content .todo-list .neon-cb-replacement .cb-wrapper .checked {
  background: #dcdcdc;
}
.todo-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.todo-list > li {
  margin: 0;
  margin-bottom: 5px;
  padding: 0;
}
.todo-list > li:last-child {
  margin-bottom: 0;
}
.todo-list > li .neon-cb-replacement .cb-wrapper {
  background: #fff;
}
.todo-list > li .neon-cb-replacement .cb-wrapper + label {
  position: relative;
}
.todo-list > li .neon-cb-replacement .cb-wrapper + label:after {
  position: absolute;
  content: '';
  height: 2px;
  background: #fff;
  width: 0%;
  top: 50%;
  left: 0;
  margin: 0;
  margin-top: 0px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.todo-list > li .neon-cb-replacement.checked .cb-wrapper + label:after {
  zoom: 1;
  filter: alpha(opacity=80);
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  width: 100%;
}
.todo-list > li .neon-cb-replacement.checked .cb-wrapper .checked {
  background: rgba(0, 0, 0, 0.25);
}
.tile-progress {
  background: #00a65b;
  margin-bottom: 20px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  background-color: #303641;
  color: #fff;
}
.tile-progress:before,
.tile-progress:after {
  content: " ";
  display: table;
}
.tile-progress:after {
  clear: both;
}
.tile-progress .tile-header {
  padding: 15px 20px;
  padding-bottom: 40px;
}
.tile-progress .tile-header h3,
.tile-progress .tile-header span {
  display: block;
  color: #fff;
}
.tile-progress .tile-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}
.tile-progress .tile-header span {
  margin-top: 4px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
}
.tile-progress .tile-progressbar {
  height: 2px;
  background: rgba(0, 0, 0, 0.18);
  margin: 0;
}
.tile-progress .tile-progressbar span {
  display: block;
  background: #fff;
  width: 0%;
  height: 100%;
  -webkit-transition: all 1.5s cubic-bezier(0.230, 1.000, 0.320, 1.000);
  -moz-transition: all 1.5s cubic-bezier(0.230, 1.000, 0.320, 1.000);
  -o-transition: all 1.5s cubic-bezier(0.230, 1.000, 0.320, 1.000);
  transition: all 1.5s cubic-bezier(0.230, 1.000, 0.320, 1.000);
}
.tile-progress .tile-footer {
  padding: 20px;
  text-align: right;
  background: rgba(0, 0, 0, 0.1);
  -webkit-border-radius: 0 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 3px;
  background-clip: padding-box;
}
.tile-progress .tile-footer h4,
.tile-progress .tile-footer > span {
  display: block;
  color: #fff;
}
.tile-progress .tile-footer h4 {
  font-size: 14px;
  margin: 0;
}
.tile-progress .tile-footer > span {
  margin-top: 4px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
}
.tile-progress .tile-header h3,
.tile-progress .tile-header a,
.tile-progress .tile-header span,
.tile-progress .tile-header i {
  color: #fff;
}
.tile-progress .tile-progressbar span {
  background: #fff;
}
.tile-progress .tile-footer h4,
.tile-progress .tile-footer > span {
  color: #fff;
}
.tile-progress .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-red {
  background-color: #f56954;
  color: #fff;
}
.tile-progress.tile-red .tile-header h3,
.tile-progress.tile-red .tile-header a,
.tile-progress.tile-red .tile-header span,
.tile-progress.tile-red .tile-header i {
  color: #fff;
}
.tile-progress.tile-red .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-red .tile-footer h4,
.tile-progress.tile-red .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-red .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-green {
  background-color: #00a65a;
  color: #fff;
}
.tile-progress.tile-green .tile-header h3,
.tile-progress.tile-green .tile-header a,
.tile-progress.tile-green .tile-header span,
.tile-progress.tile-green .tile-header i {
  color: #fff;
}
.tile-progress.tile-green .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-green .tile-footer h4,
.tile-progress.tile-green .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-green .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-blue {
  background-color: #0073b7;
  color: #fff;
}
.tile-progress.tile-blue .tile-header h3,
.tile-progress.tile-blue .tile-header a,
.tile-progress.tile-blue .tile-header span,
.tile-progress.tile-blue .tile-header i {
  color: #fff;
}
.tile-progress.tile-blue .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-blue .tile-footer h4,
.tile-progress.tile-blue .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-blue .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-aqua {
  background-color: #00c0ef;
  color: #fff;
}
.tile-progress.tile-aqua .tile-header h3,
.tile-progress.tile-aqua .tile-header a,
.tile-progress.tile-aqua .tile-header span,
.tile-progress.tile-aqua .tile-header i {
  color: #fff;
}
.tile-progress.tile-aqua .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-aqua .tile-footer h4,
.tile-progress.tile-aqua .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-aqua .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-cyan {
  background-color: #00b29e;
  color: #fff;
}
.tile-progress.tile-cyan .tile-header h3,
.tile-progress.tile-cyan .tile-header a,
.tile-progress.tile-cyan .tile-header span,
.tile-progress.tile-cyan .tile-header i {
  color: #fff;
}
.tile-progress.tile-cyan .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-cyan .tile-footer h4,
.tile-progress.tile-cyan .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-cyan .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-purple {
  background-color: #ba79cb;
  color: #fff;
}
.tile-progress.tile-purple .tile-header h3,
.tile-progress.tile-purple .tile-header a,
.tile-progress.tile-purple .tile-header span,
.tile-progress.tile-purple .tile-header i {
  color: #fff;
}
.tile-progress.tile-purple .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-purple .tile-footer h4,
.tile-progress.tile-purple .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-purple .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-pink {
  background-color: #ec3b83;
  color: #fff;
}
.tile-progress.tile-pink .tile-header h3,
.tile-progress.tile-pink .tile-header a,
.tile-progress.tile-pink .tile-header span,
.tile-progress.tile-pink .tile-header i {
  color: #fff;
}
.tile-progress.tile-pink .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-pink .tile-footer h4,
.tile-progress.tile-pink .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-pink .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-orange {
  background-color: #ffa812;
  color: #fff;
}
.tile-progress.tile-orange .tile-header h3,
.tile-progress.tile-orange .tile-header a,
.tile-progress.tile-orange .tile-header span,
.tile-progress.tile-orange .tile-header i {
  color: #fff;
}
.tile-progress.tile-orange .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-orange .tile-footer h4,
.tile-progress.tile-orange .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-orange .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-brown {
  background-color: #6c541e;
  color: #fff;
}
.tile-progress.tile-brown .tile-header h3,
.tile-progress.tile-brown .tile-header a,
.tile-progress.tile-brown .tile-header span,
.tile-progress.tile-brown .tile-header i {
  color: #fff;
}
.tile-progress.tile-brown .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-brown .tile-footer h4,
.tile-progress.tile-brown .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-brown .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-plum {
  background-color: #701c1c;
  color: #fff;
}
.tile-progress.tile-plum .tile-header h3,
.tile-progress.tile-plum .tile-header a,
.tile-progress.tile-plum .tile-header span,
.tile-progress.tile-plum .tile-header i {
  color: #fff;
}
.tile-progress.tile-plum .tile-progressbar span {
  background: #fff;
}
.tile-progress.tile-plum .tile-footer h4,
.tile-progress.tile-plum .tile-footer > span {
  color: #fff;
}
.tile-progress.tile-plum .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-progress.tile-gray {
  background-color: #f5f5f5;
  color: #8f8f8f;
}
.tile-progress.tile-gray .tile-header h3,
.tile-progress.tile-gray .tile-header a,
.tile-progress.tile-gray .tile-header span,
.tile-progress.tile-gray .tile-header i {
  color: #8f8f8f;
}
.tile-progress.tile-gray .tile-progressbar span {
  background: #8f8f8f;
}
.tile-progress.tile-gray .tile-footer h4,
.tile-progress.tile-gray .tile-footer > span {
  color: #8f8f8f;
}
.tile-progress.tile-gray .tile-footer > span {
  color: rgba(143, 143, 143, 0.7);
}
.tile-group {
  background: #303641;
  color: #fff;
  margin-bottom: 20px;
  overflow: hidden;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  display: table;
  width: 100%;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.tile-group:before,
.tile-group:after {
  content: " ";
  display: table;
}
.tile-group:after {
  clear: both;
}
.tile-group .tile-left,
.tile-group .tile-right {
  display: table-cell;
  position: relative;
}
.tile-group .tile-left .map,
.tile-group .tile-right .map {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: 1px 0;
  overflow: hidden;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.tile-group .tile-left {
  width: 40%;
}
.tile-group .tile-right {
  width: 60%;
}
.tile-group .tile-entry {
  background: rgba(0, 0, 0, 0.1);
  padding: 25px 30px;
  border-bottom: 1px solid #303641;
}
.tile-group .tile-entry span,
.tile-group .tile-entry h3,
.tile-group .tile-entry h4 {
  display: block;
  color: #fff;
  margin: 0;
}
.tile-group .tile-entry span {
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
}
.tile-group .tile-entry h3 + span,
.tile-group .tile-entry h4 + span {
  margin-top: 4px;
}
.tile-group .tile-entry .op {
  zoom: 1;
  filter: alpha(opacity=50);
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.tile-group .tile-entry:hover .op {
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.tile-group div > tile-entry:last-child {
  border-bottom: 0;
}
.tile-group .jvectormap-zoomin,
.tile-group .jvectormap-zoomout {
  width: 15px;
  height: 15px;
  margin-bottom: 4px;
}
.tile-group.tile-red {
  background-color: #ff4853;
  color: #fff;
}
.tile-group.tile-red .tile-entry {
  border-bottom-color: #ff4853;
}
.tile-group.tile-red .tile-entry h3,
.tile-group.tile-red .tile-entry h4,
.tile-group.tile-red .tile-entry a,
.tile-group.tile-red .tile-entry span,
.tile-group.tile-red .tile-entry i {
  color: #fff;
}
.tile-group.tile-red .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-red .tile-footer h4,
.tile-group.tile-red .tile-footer > span {
  color: #fff;
}
.tile-group.tile-red .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-green {
  background-color: #00a65a;
  color: #fff;
}
.tile-group.tile-green .tile-entry {
  border-bottom-color: #00a65a;
}
.tile-group.tile-green .tile-entry h3,
.tile-group.tile-green .tile-entry h4,
.tile-group.tile-green .tile-entry a,
.tile-group.tile-green .tile-entry span,
.tile-group.tile-green .tile-entry i {
  color: #fff;
}
.tile-group.tile-green .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-green .tile-footer h4,
.tile-group.tile-green .tile-footer > span {
  color: #fff;
}
.tile-group.tile-green .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-blue {
  background-color: #0073b7;
  color: #fff;
}
.tile-group.tile-blue .tile-entry {
  border-bottom-color: #0073b7;
}
.tile-group.tile-blue .tile-entry h3,
.tile-group.tile-blue .tile-entry h4,
.tile-group.tile-blue .tile-entry a,
.tile-group.tile-blue .tile-entry span,
.tile-group.tile-blue .tile-entry i {
  color: #fff;
}
.tile-group.tile-blue .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-blue .tile-footer h4,
.tile-group.tile-blue .tile-footer > span {
  color: #fff;
}
.tile-group.tile-blue .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-aqua {
  background-color: #00c0ef;
  color: #fff;
}
.tile-group.tile-aqua .tile-entry {
  border-bottom-color: #00c0ef;
}
.tile-group.tile-aqua .tile-entry h3,
.tile-group.tile-aqua .tile-entry h4,
.tile-group.tile-aqua .tile-entry a,
.tile-group.tile-aqua .tile-entry span,
.tile-group.tile-aqua .tile-entry i {
  color: #fff;
}
.tile-group.tile-aqua .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-aqua .tile-footer h4,
.tile-group.tile-aqua .tile-footer > span {
  color: #fff;
}
.tile-group.tile-aqua .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-cyan {
  background-color: #00b29e;
  color: #fff;
}
.tile-group.tile-cyan .tile-entry {
  border-bottom-color: #00b29e;
}
.tile-group.tile-cyan .tile-entry h3,
.tile-group.tile-cyan .tile-entry h4,
.tile-group.tile-cyan .tile-entry a,
.tile-group.tile-cyan .tile-entry span,
.tile-group.tile-cyan .tile-entry i {
  color: #fff;
}
.tile-group.tile-cyan .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-cyan .tile-footer h4,
.tile-group.tile-cyan .tile-footer > span {
  color: #fff;
}
.tile-group.tile-cyan .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-purple {
  background-color: #ba79cb;
  color: #fff;
}
.tile-group.tile-purple .tile-entry {
  border-bottom-color: #ba79cb;
}
.tile-group.tile-purple .tile-entry h3,
.tile-group.tile-purple .tile-entry h4,
.tile-group.tile-purple .tile-entry a,
.tile-group.tile-purple .tile-entry span,
.tile-group.tile-purple .tile-entry i {
  color: #fff;
}
.tile-group.tile-purple .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-purple .tile-footer h4,
.tile-group.tile-purple .tile-footer > span {
  color: #fff;
}
.tile-group.tile-purple .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-pink {
  background-color: #ec3b83;
  color: #fff;
}
.tile-group.tile-pink .tile-entry {
  border-bottom-color: #ec3b83;
}
.tile-group.tile-pink .tile-entry h3,
.tile-group.tile-pink .tile-entry h4,
.tile-group.tile-pink .tile-entry a,
.tile-group.tile-pink .tile-entry span,
.tile-group.tile-pink .tile-entry i {
  color: #fff;
}
.tile-group.tile-pink .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-pink .tile-footer h4,
.tile-group.tile-pink .tile-footer > span {
  color: #fff;
}
.tile-group.tile-pink .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-orange {
  background-color: #ffa812;
  color: #fff;
}
.tile-group.tile-orange .tile-entry {
  border-bottom-color: #ffa812;
}
.tile-group.tile-orange .tile-entry h3,
.tile-group.tile-orange .tile-entry h4,
.tile-group.tile-orange .tile-entry a,
.tile-group.tile-orange .tile-entry span,
.tile-group.tile-orange .tile-entry i {
  color: #fff;
}
.tile-group.tile-orange .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-orange .tile-footer h4,
.tile-group.tile-orange .tile-footer > span {
  color: #fff;
}
.tile-group.tile-orange .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-brown {
  background-color: #6c541e;
  color: #fff;
}
.tile-group.tile-brown .tile-entry {
  border-bottom-color: #6c541e;
}
.tile-group.tile-brown .tile-entry h3,
.tile-group.tile-brown .tile-entry h4,
.tile-group.tile-brown .tile-entry a,
.tile-group.tile-brown .tile-entry span,
.tile-group.tile-brown .tile-entry i {
  color: #fff;
}
.tile-group.tile-brown .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-brown .tile-footer h4,
.tile-group.tile-brown .tile-footer > span {
  color: #fff;
}
.tile-group.tile-brown .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-plum {
  background-color: #701c1c;
  color: #fff;
}
.tile-group.tile-plum .tile-entry {
  border-bottom-color: #701c1c;
}
.tile-group.tile-plum .tile-entry h3,
.tile-group.tile-plum .tile-entry h4,
.tile-group.tile-plum .tile-entry a,
.tile-group.tile-plum .tile-entry span,
.tile-group.tile-plum .tile-entry i {
  color: #fff;
}
.tile-group.tile-plum .tile-progressbar span {
  background: #fff;
}
.tile-group.tile-plum .tile-footer h4,
.tile-group.tile-plum .tile-footer > span {
  color: #fff;
}
.tile-group.tile-plum .tile-footer > span {
  color: rgba(255, 255, 255, 0.7);
}
.tile-group.tile-gray {
  background-color: #f5f5f5;
  color: #8f8f8f;
}
.tile-group.tile-gray .tile-entry {
  border-bottom-color: #f5f5f5;
}
.tile-group.tile-gray .tile-entry h3,
.tile-group.tile-gray .tile-entry h4,
.tile-group.tile-gray .tile-entry a,
.tile-group.tile-gray .tile-entry span,
.tile-group.tile-gray .tile-entry i {
  color: #8f8f8f;
}
.tile-group.tile-gray .tile-progressbar span {
  background: #8f8f8f;
}
.tile-group.tile-gray .tile-footer h4,
.tile-group.tile-gray .tile-footer > span {
  color: #8f8f8f;
}
.tile-group.tile-gray .tile-footer > span {
  color: rgba(143, 143, 143, 0.7);
}
.page-error-404 {
  color: #303641;
  text-align: center;
}
.page-error-404 .error-symbol {
  font-size: 120px;
}
.page-error-404 .error-text {
  padding-bottom: 25px;
  font-size: 16px;
}
.page-error-404 .error-text h2 {
  font-size: 45px;
}
.page-error-404 .error-text p {
  font-size: 22px;
}
.page-error-404 .error-text + hr {
  margin-bottom: 50px;
}
.page-error-404 .input-group {
  width: 250px;
  margin: 0 auto;
}
body.page-fade {
  background: #303641;
  -webkit-transition: 400ms all ease-in-out;
  -moz-transition: 400ms all ease-in-out;
  -o-transition: 400ms all ease-in-out;
  transition: 400ms all ease-in-out;
  -webkit-transform-origin: 50% 30%;
  -moz-transform-origin: 50% 30%;
  -ms-transform-origin: 50% 30%;
  -o-transform-origin: 50% 30%;
  transform-origin: 50% 30%;
}
body.page-fade > .page-container {
  -webkit-transform: scale(0.9) translateY(100px);
  -moz-transform: scale(0.9) translateY(100px);
  -ms-transform: scale(0.9) translateY(100px);
  -o-transform: scale(0.9) translateY(100px);
  transform: scale(0.9) translateY(100px);
  -webkit-transition: 400ms all ease-in-out;
  -moz-transition: 400ms all ease-in-out;
  -o-transition: 400ms all ease-in-out;
  transition: 400ms all ease-in-out;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
body.page-fade-init {
  background: #ffffff;
}
body.page-fade-init > .page-container {
  -webkit-transform: scale(1) translateY(0px);
  -moz-transform: scale(1) translateY(0px);
  -ms-transform: scale(1) translateY(0px);
  -o-transform: scale(1) translateY(0px);
  transform: scale(1) translateY(0px);
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
body.page-left-in {
  background: #303641;
  -webkit-transition: 400ms all ease-in-out;
  -moz-transition: 400ms all ease-in-out;
  -o-transition: 400ms all ease-in-out;
  transition: 400ms all ease-in-out;
  -webkit-transform-origin: 0% 50%;
  -moz-transform-origin: 0% 50%;
  -ms-transform-origin: 0% 50%;
  -o-transform-origin: 0% 50%;
  transform-origin: 0% 50%;
  -webkit-perspective: 1000;
  -moz-perspective: 1000;
  perspective: 1000;
  -webkit-perspective: 1000px;
  -moz-perspective: 1000px;
  perspective: 1000px;
}
body.page-left-in > .page-container {
  -webkit-transform: rotateY(8deg) translateX(-100px);
  -moz-transform: rotateY(8deg) translateX(-100px);
  -ms-transform: rotateY(8deg) translateX(-100px);
  -o-transform: rotateY(8deg) translateX(-100px);
  transform: rotateY(8deg) translateX(-100px);
  -webkit-transition: 400ms all ease-in-out;
  -moz-transition: 400ms all ease-in-out;
  -o-transition: 400ms all ease-in-out;
  transition: 400ms all ease-in-out;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
body.page-left-in-init {
  background: #ffffff;
}
body.page-left-in-init > .page-container {
  -webkit-transform: rotateY(0deg) translateX(0px);
  -moz-transform: rotateY(0deg) translateX(0px);
  -ms-transform: rotateY(0deg) translateX(0px);
  -o-transform: rotateY(0deg) translateX(0px);
  transform: rotateY(0deg) translateX(0px);
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
body.page-right-in {
  background: #303641;
  overflow-x: hidden;
  -webkit-transition: 400ms all ease-in-out;
  -moz-transition: 400ms all ease-in-out;
  -o-transition: 400ms all ease-in-out;
  transition: 400ms all ease-in-out;
  -webkit-transform-origin: 100% 50%;
  -moz-transform-origin: 100% 50%;
  -ms-transform-origin: 100% 50%;
  -o-transform-origin: 100% 50%;
  transform-origin: 100% 50%;
  -webkit-perspective: 1000;
  -moz-perspective: 1000;
  perspective: 1000;
  -webkit-perspective: 1000px;
  -moz-perspective: 1000px;
  perspective: 1000px;
}
body.page-right-in > .page-container {
  -webkit-transform: rotateY(-8deg) translateX(100px);
  -moz-transform: rotateY(-8deg) translateX(100px);
  -ms-transform: rotateY(-8deg) translateX(100px);
  -o-transform: rotateY(-8deg) translateX(100px);
  transform: rotateY(-8deg) translateX(100px);
  -webkit-transition: 400ms all ease-in-out;
  -moz-transition: 400ms all ease-in-out;
  -o-transition: 400ms all ease-in-out;
  transition: 400ms all ease-in-out;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
body.page-right-in-init {
  background: #ffffff;
}
body.page-right-in-init > .page-container {
  -webkit-transform: rotateY(0deg) translateX(0px);
  -moz-transform: rotateY(0deg) translateX(0px);
  -ms-transform: rotateY(0deg) translateX(0px);
  -o-transform: rotateY(0deg) translateX(0px);
  transform: rotateY(0deg) translateX(0px);
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
body.page-fade-only {
  background: #303641;
  -webkit-transition: 900ms all cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -moz-transition: 900ms all cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -o-transition: 900ms all cubic-bezier(0.445, 0.050, 0.550, 0.950);
  transition: 900ms all cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -webkit-transform-origin: 50% 30%;
  -moz-transform-origin: 50% 30%;
  -ms-transform-origin: 50% 30%;
  -o-transform-origin: 50% 30%;
  transform-origin: 50% 30%;
}
body.page-fade-only > .page-container {
  -webkit-transition: 900ms all cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -moz-transition: 900ms all cubic-bezier(0.445, 0.050, 0.550, 0.950);
  -o-transition: 900ms all cubic-bezier(0.445, 0.050, 0.550, 0.950);
  transition: 900ms all cubic-bezier(0.445, 0.050, 0.550, 0.950);
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
body.page-fade-only-init {
  background: #ffffff;
}
body.page-fade-only-init > .page-container {
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.draggable-portlets .sorted {
  min-height: 100px;
}
.draggable-portlets .sorted .ui-sortable-placeholder {
  background: rgba(255, 255, 204, 0.7);
  border: 1px dashed #ebebeb;
  visibility: visible !important;
}
.draggable-portlets .sorted > .panel {
  -webkit-transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  -o-transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  -webkit-transition: opacity 300ms ease-in-out, -webkit-transform 300ms ease-in-out;
  -moz-transition: opacity 300ms ease-in-out, -moz-transform 300ms ease-in-out;
  -o-transition: opacity 300ms ease-in-out, -o-transform 300ms ease-in-out;
  transition: opacity 300ms ease-in-out,-webkit-transform 300ms ease-in-out,-moz-transform 300ms ease-in-out,-o-transform 300ms ease-in-out,transform 300ms ease-in-out;
}
.draggable-portlets .sorted > .panel .panel-heading {
  cursor: pointer;
}
.draggable-portlets.dragging .sorted > .panel {
  -webkit-transform: scale(.9);
  -moz-transform: scale(.9);
  -ms-transform: scale(.9);
  -o-transform: scale(.9);
  transform: scale(.9);
  zoom: 1;
  filter: alpha(opacity=90);
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
}
.draggable-portlets.dragging .sorted > .panel.ui-sortable-helper {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
  zoom: 1;
  filter: alpha(opacity=100);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.language-selector {
  margin-top: -7px;
}
.language-selector > .dropdown-toggle {
  display: inline-block;
  padding: 7px 12px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.language-selector > .dropdown-toggle img {
  display: inline-block;
  line-height: 1;
}
.language-selector.open > .dropdown-toggle {
  background: #f5f5f6;
}
.language-selector .dropdown-menu {
  background: #f5f5f6;
  border: none;
  margin: 0;
  padding: 0;
  width: 200px;
  overflow: hidden;
  -webkit-border-radius: 0 3px 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 3px;
  background-clip: padding-box;
}
.language-selector .dropdown-menu.pull-right {
  -webkit-border-radius: 3px 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 3px 0 3px 3px;
  background-clip: padding-box;
  margin-right: 5px;
}
.language-selector .dropdown-menu > li {
  border-bottom: 1px solid #ebebeb;
}
.language-selector .dropdown-menu > li a {
  margin: 0;
  display: block;
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
  padding: 10px 20px;
  color: #8d929a;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.language-selector .dropdown-menu > li a img {
  margin-right: 5px;
}
.language-selector .dropdown-menu > li a:hover {
  background: rgba(235, 235, 235, 0.4);
}
.language-selector .dropdown-menu > li:last-child {
  border-bottom: 0;
}
.language-selector .dropdown-menu > li.active a {
  background: rgba(235, 235, 235, 0.9);
  color: #737881;
}
.language-selector.open > .dropdown-toggle {
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.search-results-env .search-string {
  font-size: 17px;
}
.search-results-env .search-string strong {
  color: #ec5956;
  font-weight: normal;
}
.search-results-env .nav-tabs {
  margin-bottom: 0;
  border-bottom-color: #ebebeb;
}
.search-results-env .nav-tabs li a {
  background: #f5f5f6;
  border-color: #ebebeb;
}
.search-results-env .nav-tabs li.active a {
  background: #f9f9f9;
  border-bottom-color: transparent;
}
.search-results-env .search-bar {
  padding: 25px;
  background: #f9f9f9;
  margin-bottom: 30px;
}
.search-results-env .search-bar .form-control + .input-group-btn {
  border: 1px solid #303641;
  border-left: 0;
  padding-left: 3px;
  background-color: #303641;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.search-results-env .search-bar .form-control + .input-group-btn .button {
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
}
.search-results-env .search-results-panes {
  position: relative;
}
.search-results-env .search-results-panes .search-results-pane {
  position: relative;
  display: none;
}
.search-results-env .search-results-panes .search-results-pane.active {
  display: block;
}
.search-results-env .search-results {
  list-style: none;
  margin: 0;
  padding: 0;
}
.search-results-env .search-results > .search-result {
  border-bottom: 1px solid #f3f3f3;
  padding-bottom: 20px;
  margin: 0;
  margin-bottom: 20px;
}
.search-results-env .search-results > .search-result h4 {
  font-size: 18px;
  margin: 0;
  margin-bottom: 10px;
}
.search-results-env .search-results > .search-result .link {
  color: #ec5956;
}
.search-results-env .search-results > .search-result .link:hover {
  text-decoration: underline;
}
.search-results-env .search-results > .search-result:last-child {
  padding-bottom: 0;
  border-bottom: 0;
}
.search-results-env .search-results > .search-result .sr-inner {
  padding: 15px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.search-results-env .search-results > .search-result .sr-inner:hover {
  background: #f9f9f9;
}
.search-results-env .search-results-messages thead tr th,
.search-results-env .search-results-messages tbody tr th,
.search-results-env .search-results-messages thead tr td,
.search-results-env .search-results-messages tbody tr td {
  padding: 10px 10px;
}
.search-results-env .search-results-messages tbody tr {
  background: #f9f9f9;
}
.search-results-env .search-results-messages tbody tr.unread {
  background: #FFF;
}
.search-results-env .search-results-messages tbody tr.unread > td:nth-child(2) a {
  font-weight: bold;
}
.search-results-env .search-results-messages tbody tr .star {
  display: inline-block;
  color: #dddddd;
  margin-right: 5px;
  font-size: 14px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.search-results-env .search-results-messages tbody tr .star.stared,
.search-results-env .search-results-messages tbody tr .star.starred {
  color: #ff9600;
}
@media (max-width: 570px) {
  .search-results-env .nav-tabs {
    text-align: left;
  }
  .search-results-env .nav-tabs > li {
    float: none !important;
  }
  .search-results-env .nav-tabs > li.pull-left {
    display: block;
    width: 100%;
  }
  .search-results-env .nav-tabs > li .search-string {
    padding-bottom: 15px;
  }
}
.boxed-layout {
  background: url(../images/boxed-pattern-3.png);
  padding-bottom: 0px;
}
.boxed-layout.page-fade,
.boxed-layout.page-fade-only,
.boxed-layout.page-left-in,
.boxed-layout.page-right-in {
  background: url(../images/boxed-pattern-3.png);
}
.boxed-layout .page-container {
  position: relative;
  width: 80%;
  margin: 0 auto;
  margin-top: 0px;
  -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
}
.boxed-layout .page-container:before,
.boxed-layout .page-container:after {
  content: " ";
  display: table;
}
.boxed-layout .page-container:after {
  clear: both;
}
.boxed-layout .page-container.horizontal-menu > header.navbar {
  width: 80%;
  margin: 0 auto;
  -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
}
.boxed-layout .page-container.horizontal-menu .main-content {
  padding-left: 0;
  padding-right: 0;
}
.boxed-layout .page-container.horizontal-menu .main-content .container {
  width: 100%;
}
.boxed-layout #chat {
  position: absolute;
}
@media screen and (max-width: 992px) {
  .boxed-layout .page-container {
    position: absolute;
    width: 100%;
    overflow-x: visible;
  }
  .boxed-layout #chat {
    position: fixed;
  }
}
@media (max-width: 1199px) {
  .pull-none-md {
    float: none !important;
  }
  .pull-none-md .dropdown-menu {
    left: 0;
    right: auto;
  }
  .pull-left-md {
    float: left !important;
  }
  .pull-left-md .dropdown-menu {
    left: 0;
    right: auto;
  }
  .pull-right-md {
    float: right !important;
  }
  .pull-right-md .dropdown-menu {
    left: auto;
    right: 0;
  }
}
@media (max-width: 991px) {
  .pull-none-sm {
    float: none !important;
  }
  .pull-none-sm .dropdown-menu {
    left: 0;
    right: auto;
  }
  .pull-left-sm {
    float: left !important;
  }
  .pull-left-sm .dropdown-menu {
    left: 0;
    right: auto;
  }
  .pull-right-sm {
    float: right !important;
  }
  .pull-right-sm .dropdown-menu {
    left: auto;
    right: 0;
  }
}
@media (max-width: 767px) {
  .pull-none-xs {
    float: none !important;
  }
  .pull-none-xs .dropdown-menu {
    left: 0;
    right: auto;
  }
  .pull-left-xs {
    float: left !important;
  }
  .pull-left-xs .dropdown-menu {
    left: 0;
    right: auto;
  }
  .pull-right-xs {
    float: right !important;
  }
  .pull-right-xs .dropdown-menu {
    left: auto;
    right: 0;
  }
}
@media (max-width: 480px - 140) {
  .pull-none-xsm {
    float: none !important;
  }
  .pull-none-xsm .dropdown-menu {
    left: 0;
    right: auto;
  }
  .pull-left-xsm {
    float: left !important;
  }
  .pull-left-xsm .dropdown-menu {
    left: 0;
    right: auto;
  }
  .pull-right-xsm {
    float: right !important;
  }
  .pull-right-xsm .dropdown-menu {
    left: auto;
    right: 0;
  }
}
