<!doctype html>

<title>CodeMirror: Pig Latin mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="pig.js"></script>
<style>.CodeMirror {border: 2px inset #dee;}</style>
<div id=nav>
  <a href="http://codemirror.net"><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/marijnh/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Pig Latin</a>
  </ul>
</div>

<article>
<h2>Pig Latin mode</h2>
<form><textarea id="code" name="code">
-- Apache Pig (Pig Latin Language) Demo
/* 
This is a multiline comment.
*/
a = LOAD "\path\to\input" USING PigStorage('\t') AS (x:long, y:chararray, z:bytearray);
b = GROUP a BY (x,y,3+4);
c = FOREACH b GENERATE flatten(group) as (x,y), SUM(group.$2) as z;
STORE c INTO "\path\to\output";

--
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        indentUnit: 4,
        mode: "text/x-pig"
      });
    </script>

    <p>
        Simple mode that handles Pig Latin language.
    </p>

    <p><strong>MIME type defined:</strong> <code>text/x-pig</code>
    (PIG code)
</html>
</article>
